<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TiaXmlGenerator.Models</name>
    </assembly>
    <members>
        <member name="T:TiaXmlGenerator.Models.InstanceData">
            <summary>
            实例数据模型，对应Python中的instance_data字典
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Models.InstanceData.GetExcelValue(System.String)">
            <summary>
            获取Excel列数据
            </summary>
            <param name="columnName">列名</param>
            <returns>列值</returns>
        </member>
        <member name="M:TiaXmlGenerator.Models.InstanceData.SetExcelValue(System.String,System.String)">
            <summary>
            设置Excel列数据
            </summary>
            <param name="columnName">列名</param>
            <param name="value">列值</param>
        </member>
        <member name="T:TiaXmlGenerator.Models.OpModeData">
            <summary>
            OpMode数据模型
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.GenerationConfig">
            <summary>
            生成配置
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.ParameterMapping">
            <summary>
            参数映射配置
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.PartsModificationRule">
            <summary>
            Parts修改规则
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.ComponentRule">
            <summary>
            组件规则
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.AddressRule">
            <summary>
            地址规则
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.ConstantRule">
            <summary>
            常量规则
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Models.ExcelReadResult">
            <summary>
            Excel读取结果
            </summary>
        </member>
    </members>
</doc>
