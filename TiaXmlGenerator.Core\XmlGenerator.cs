using Microsoft.Extensions.Logging;
using System.Xml.Linq;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

namespace TiaXmlGenerator.Core
{
    /// <summary>
    /// XML生成器核心类，对应Python中的XMLGenerator类
    /// </summary>
    public class XmlGenerator : IXmlGeneratorService
    {
        private readonly IExcelReaderService _excelReader;
        private readonly INetworkGeneratorService _networkGenerator;
        private readonly IIdGeneratorService _idGenerator;
        private readonly IFileService _fileService;
        private readonly IXmlProcessorService _xmlProcessor;
        private readonly ILogger<XmlGenerator> _logger;

        private XDocument? _xmlDocument;
        private XElement? _rootElement;
        private XNamespace? _xmlNamespace;

        // 实例参数映射，对应Python中的INSTANCE_PARAM_MAPPING
        private static readonly Dictionary<string, ParameterMapping> InstanceParamMapping = new()
        {
            ["forward_sensor"] = new() { Section = "Input", Member = "xSnsFwd", IsBool = false },
            ["backward_sensor"] = new() { Section = "Input", Member = "xSnsBwd", IsBool = false },
            ["forward_output"] = new() { Section = "Output", Member = "xCmdFwd", IsBool = false },
            ["backward_output"] = new() { Section = "Output", Member = "xCmdBwd", IsBool = false },
            ["auto_forward"] = new() { Section = "Input", Member = "bAutoFwd", IsBool = true },
            ["auto_backward"] = new() { Section = "Input", Member = "bAutoBwd", IsBool = true },
            ["forward_permit"] = new() { Section = "Input", Member = "bPermitFwd", IsBool = true },
            ["backward_permit"] = new() { Section = "Input", Member = "bPermitBwd", IsBool = true }
        };

        // Parts修改规则 - 单输入类型
        private static readonly Dictionary<string, PartsModificationRule> PartsModificationsSingleInput = new()
        {
            ["21"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a", NewNameExcelCol = "I点-工作位1" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "I点-工作位1偏移量" }
                }
            },
            ["22"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b", NewNameExcelCol = "I点-原位1" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "I点-原位1偏移量" }
                }
            },
            ["23"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                    new() { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                    new() { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                }
            },
            ["29"] = new()
            {
                Constants = new List<ConstantRule>
                {
                    new() { OriginalValue = "12", NewValueExcelCol = "OpMode编号" }
                }
            },
            ["35"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "Q216.4 NA1C101挡料气缸1伸出", NewNameExcelCol = "Q点-工作位" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "Q点-工作位偏移量" }
                }
            },
            ["36"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "Q216.5 NA1C101挡料气缸1缩回", NewNameExcelCol = "Q点-原位" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "Q点-原位偏移量" }
                }
            }
        };

        public event EventHandler<string>? LogMessage;
        public event EventHandler<int>? ProgressChanged;
        public event EventHandler<string>? StatusChanged;

        public XmlGenerator(
            IExcelReaderService excelReader,
            INetworkGeneratorService networkGenerator,
            IIdGeneratorService idGenerator,
            IFileService fileService,
            IXmlProcessorService xmlProcessor,
            ILogger<XmlGenerator> logger)
        {
            _excelReader = excelReader;
            _networkGenerator = networkGenerator;
            _idGenerator = idGenerator;
            _fileService = fileService;
            _xmlProcessor = xmlProcessor;
            _logger = logger;
        }

        /// <summary>
        /// 生成XML文件 - 严格按照Python代码的generate_xml方法实现
        /// </summary>
        public async Task<bool> GenerateXmlAsync(GenerationConfig config)
        {
            try
            {
                OnStatusChanged("开始生成XML文件...");
                OnProgressChanged(0);
                OnLogMessage($"开始生成XML文件: {config.OutputFilePath}");

                // 验证输入文件
                if (!await ValidateInputFilesAsync(config))
                    return false;

                OnProgressChanged(10);

                // 读取Excel数据
                OnStatusChanged("读取Excel数据...");
                var excelResult = await _excelReader.ReadExcelDataAsync(config.ExcelFilePath);
                if (excelResult.Instances == null || !excelResult.Instances.Any())
                {
                    OnLogMessage("未读取到Excel数据或实例列表为空。请检查Excel文件。");
                    return false;
                }

                OnLogMessage($"成功读取 {excelResult.Instances.Count} 个实例数据");
                OnProgressChanged(30);

                // 使用基于文本行的处理方式，严格按照Python代码实现
                return await GenerateXmlUsingTextProcessing(config, excelResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成XML时发生错误");
                OnLogMessage($"生成XML时发生错误：{ex.Message}");
                OnStatusChanged("生成失败");
                return false;
            }
        }

        /// <summary>
        /// 使用基于文本处理的方式生成XML，严格按照Python代码实现
        /// </summary>
        private async Task<bool> GenerateXmlUsingTextProcessing(GenerationConfig config, ExcelReadResult excelResult)
        {
            try
            {
                // 创建备份目录（如果不存在）
                var backupDir = Path.Combine(Path.GetDirectoryName(Path.GetFullPath(config.OutputFilePath)) ?? "", "backups");
                Directory.CreateDirectory(backupDir);

                // 如果输出文件已存在，创建备份
                if (File.Exists(config.OutputFilePath))
                {
                    var backupTime = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var backupFile = Path.Combine(backupDir, $"backup_{backupTime}_{Path.GetFileName(config.OutputFilePath)}");
                    File.Copy(config.OutputFilePath, backupFile, true);
                    OnLogMessage($"已创建备份: {backupFile}");
                }

                // 读取模板文件
                OnStatusChanged("读取模板文件...");
                OnLogMessage($"读取模板文件: {config.TemplateFilePath}");
                var templateLines = await File.ReadAllLinesAsync(config.TemplateFilePath, System.Text.Encoding.UTF8);
                OnProgressChanged(40);

                var outputLines = new List<string>();

                // 1. 复制XML头部（1-65行）
                OnStatusChanged("复制XML头部...");
                OnLogMessage("复制XML头部（1-65行）");
                var headerLines = templateLines.Take(65).ToArray();
                outputLines.AddRange(headerLines);

                // 2. 生成实例声明
                OnStatusChanged("生成实例声明...");
                OnLogMessage("生成实例声明内容");
                await GenerateInstanceDeclarations(templateLines, excelResult.Instances, outputLines);
                OnProgressChanged(60);

                // 3. 添加固定变量声明
                OnStatusChanged("生成固定变量声明...");
                OnLogMessage("添加固定变量声明（3028-3076行）");
                GenerateFixedVariableDeclarations(templateLines, excelResult.Instances, outputLines);

                // 4. 添加HMI/mode固定内容
                OnStatusChanged("添加HMI/mode固定内容...");
                OnLogMessage("添加HMI/mode固定内容（3077-3459行）- 直接复制，不做任何修改");
                var hmiModeLines = templateLines.Skip(3076).Take(383).ToArray(); // 从3077到3459行
                outputLines.AddRange(hmiModeLines);
                OnProgressChanged(70);

                // 5. 生成各实例的网络代码
                OnStatusChanged("生成实例网络代码...");
                OnLogMessage("生成实例网络代码");
                await GenerateInstanceNetworks(templateLines, excelResult.Instances, excelResult.OpModeComment, outputLines);
                OnProgressChanged(90);

                // 将所有内容合并为单一字符串并保存
                OnStatusChanged("保存XML文件...");
                var outputContent = string.Join(Environment.NewLine, outputLines);
                await File.WriteAllTextAsync(config.OutputFilePath, outputContent, System.Text.Encoding.UTF8);

                OnProgressChanged(100);
                OnStatusChanged("XML生成完成");
                OnLogMessage($"XML文件已成功生成：{config.OutputFilePath}");

                // 计算文件统计信息
                var lineCount = outputLines.Count;
                var fileSize = new FileInfo(config.OutputFilePath).Length / 1024.0; // KB
                OnLogMessage($"生成的XML文件共有 {lineCount} 行");
                OnLogMessage($"生成的XML文件大小: {fileSize:F2} KB");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用文本处理方式生成XML时发生错误");
                OnLogMessage($"生成XML时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成实例声明，对应Python代码中的实例声明生成部分
        /// </summary>
        private async Task GenerateInstanceDeclarations(string[] templateLines, List<InstanceData> instances, List<string> outputLines)
        {
            var instanceTemplateLines = templateLines.Skip(65).Take(371).ToArray(); // 模板中的实例声明部分（66-436行）

            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                var instanceLines = new List<string>(instanceTemplateLines);

                // 处理第66行 Member Name
                if (instanceLines.Count > 0)
                {
                    var line = instanceLines[0];
                    if (line.Contains("Member Name=\""))
                    {
                        // 提取Member Name后的原始名称
                        var startIdx = line.IndexOf("Member Name=\"") + "Member Name=\"".Length;
                        var endIdx = line.IndexOf("\"", startIdx);
                        if (endIdx > startIdx)
                        {
                            var originalName = line.Substring(startIdx, endIdx - startIdx);
                            // 替换为实例名称 (G列内容)
                            instanceLines[0] = line.Replace($"Member Name=\"{originalName}\"", $"Member Name=\"{instance.Name}\"");
                            OnLogMessage($"已将第66行的Member Name从'{originalName}'修改为'{instance.Name}'");
                        }
                    }
                }

                // 处理第68行 IntegerAttribute Name="Offset" 的值
                for (int j = 0; j < instanceLines.Count; j++)
                {
                    var line = instanceLines[j];
                    // 只修改实例声明的整体偏移量（第68行），不修改实例内部的偏移量
                    if (line.Contains("<IntegerAttribute Name=\"Offset\" Informative=\"true\" SystemDefined=\"true\">") && j < 5)
                    {
                        // 替换为递增的偏移量，每个实例递增256
                        instanceLines[j] = line.Replace(
                            "<IntegerAttribute Name=\"Offset\" Informative=\"true\" SystemDefined=\"true\">0</IntegerAttribute>",
                            $"<IntegerAttribute Name=\"Offset\" Informative=\"true\" SystemDefined=\"true\">{i * 256}</IntegerAttribute>");
                        OnLogMessage($"已将实例 '{instance.Name}' 的整体偏移量设置为 {i * 256}");
                        break; // 只处理第一个匹配的偏移量
                    }
                }

                // 处理第78行 MultiLanguageText
                for (int j = 0; j < instanceLines.Count; j++)
                {
                    var line = instanceLines[j];
                    if (line.Contains("<MultiLanguageText Lang=\"zh-CN\">"))
                    {
                        // 提取原始文本
                        var startIdx = line.IndexOf("<MultiLanguageText Lang=\"zh-CN\">") + "<MultiLanguageText Lang=\"zh-CN\">".Length;
                        var endIdx = line.IndexOf("</MultiLanguageText>");
                        if (endIdx > startIdx)
                        {
                            var originalText = line.Substring(startIdx, endIdx - startIdx);
                            // 替换为实例注释 (H列内容)，而不是实例名称
                            instanceLines[j] = line.Replace(
                                $"<MultiLanguageText Lang=\"zh-CN\">{originalText}</MultiLanguageText>",
                                $"<MultiLanguageText Lang=\"zh-CN\">{instance.Comment}</MultiLanguageText>");
                            OnLogMessage($"已将MultiLanguageText从'{originalText}'修改为'{instance.Comment}'");
                        }
                    }
                }

                // 将处理后的行添加到输出
                outputLines.AddRange(instanceLines);
            }
        }

        /// <summary>
        /// 生成固定变量声明，对应Python代码中的固定变量声明部分
        /// </summary>
        private void GenerateFixedVariableDeclarations(string[] templateLines, List<InstanceData> instances, List<string> outputLines)
        {
            var fixedVarsLines = templateLines.Skip(3027).Take(49).ToList(); // 从3028到3076行的内容

            // 计算BOOL变量的偏移地址
            var lastInstanceOffset = instances.Count > 0 ? (instances.Count - 1) * 256 : 0;

            // staManual的偏移地址是最后一个实例声明的偏移地址+256
            var staManualOffset = lastInstanceOffset + 256;
            // staAuto的偏移地址是staManual的偏移地址+1
            var staAutoOffset = staManualOffset + 1;
            // statReset的偏移地址是staAuto的偏移地址+1
            var statResetOffset = staAutoOffset + 1;

            OnLogMessage($"设置固定变量偏移地址：staManual={staManualOffset}, staAuto={staAutoOffset}, statReset={statResetOffset}");

            // 处理fixedVarsLines，修改三个变量的偏移地址
            for (int i = 0; i < fixedVarsLines.Count; i++)
            {
                var line = fixedVarsLines[i];

                // 修改staManual的偏移地址
                if (line.Contains("<Member Name=\"staManual\""))
                {
                    for (int j = i + 1; j < fixedVarsLines.Count; j++)
                    {
                        if (fixedVarsLines[j].Contains("<IntegerAttribute Name=\"Offset\""))
                        {
                            var startIdx = fixedVarsLines[j].IndexOf('>') + 1;
                            var endIdx = fixedVarsLines[j].IndexOf("</");
                            if (endIdx > startIdx)
                            {
                                fixedVarsLines[j] = fixedVarsLines[j].Substring(0, startIdx) + staManualOffset + fixedVarsLines[j].Substring(endIdx);
                                OnLogMessage($"已将staManual的偏移地址设置为{staManualOffset}");
                            }
                            break;
                        }
                    }
                }
                // 修改staAuto的偏移地址
                else if (line.Contains("<Member Name=\"staAuto\""))
                {
                    for (int j = i + 1; j < fixedVarsLines.Count; j++)
                    {
                        if (fixedVarsLines[j].Contains("<IntegerAttribute Name=\"Offset\""))
                        {
                            var startIdx = fixedVarsLines[j].IndexOf('>') + 1;
                            var endIdx = fixedVarsLines[j].IndexOf("</");
                            if (endIdx > startIdx)
                            {
                                fixedVarsLines[j] = fixedVarsLines[j].Substring(0, startIdx) + staAutoOffset + fixedVarsLines[j].Substring(endIdx);
                                OnLogMessage($"已将staAuto的偏移地址设置为{staAutoOffset}");
                            }
                            break;
                        }
                    }
                }
                // 修改statReset的偏移地址
                else if (line.Contains("<Member Name=\"statReset\""))
                {
                    for (int j = i + 1; j < fixedVarsLines.Count; j++)
                    {
                        if (fixedVarsLines[j].Contains("<IntegerAttribute Name=\"Offset\""))
                        {
                            var startIdx = fixedVarsLines[j].IndexOf('>') + 1;
                            var endIdx = fixedVarsLines[j].IndexOf("</");
                            if (endIdx > startIdx)
                            {
                                fixedVarsLines[j] = fixedVarsLines[j].Substring(0, startIdx) + statResetOffset + fixedVarsLines[j].Substring(endIdx);
                                OnLogMessage($"已将statReset的偏移地址设置为{statResetOffset}");
                            }
                            break;
                        }
                    }
                }
            }

            outputLines.AddRange(fixedVarsLines);
        }

        /// <summary>
        /// 生成各实例的网络代码，对应Python代码中的实例网络生成部分
        /// </summary>
        private async Task GenerateInstanceNetworks(string[] templateLines, List<InstanceData> instances, string opModeComment, List<string> outputLines)
        {
            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                var isLast = (i == instances.Count - 1);

                OnLogMessage($"生成实例 {i + 1}/{instances.Count} 的网络代码: {instance.Name}");

                // 根据实例类型选择不同的网络模板
                string[] networkTemplateLines;
                if (instance.IsDoubleInputType)
                {
                    // 双输入类型，使用5625-6099行的模板
                    OnLogMessage($"实例 {instance.Name} 是双输入类型，使用模板行5625-6099");
                    networkTemplateLines = templateLines.Skip(5624).Take(475).ToArray(); // 从5625到6099行的内容
                }
                else
                {
                    // 单输入类型，使用3460-3892行的模板
                    OnLogMessage($"实例 {instance.Name} 是单输入类型，使用模板行3460-3892");
                    networkTemplateLines = templateLines.Skip(3459).Take(433).ToArray(); // 从3460到3892行的内容
                }

                // 网络代码内容处理
                var networkContent = string.Join(Environment.NewLine, networkTemplateLines);

                // 处理网络内容（标题等）
                networkContent = CustomizeNetworkContent(networkContent, instance, i);

                // 处理最后一个实例的特殊情况
                if (isLast)
                {
                    OnLogMessage("这是最后一个实例，替换其末尾部分（6967-7023行）");

                    // 使用正则表达式更精确地匹配结束标记
                    var match = System.Text.RegularExpressions.Regex.Match(networkContent, @"</FlgNet>\s*</NetworkSource>");

                    if (match.Success)
                    {
                        // 找到匹配，截取到匹配位置结束
                        var footerStart = match.Index;
                        networkContent = networkContent.Substring(0, footerStart);
                        // 添加结束标记，确保XML结构完整
                        networkContent += "</FlgNet></NetworkSource>";

                        // 处理尾部内容 (模板 6967-7023行)
                        var footerLines = templateLines.Skip(6966).Take(57).ToArray();
                        var processedFooterLines = new List<string>();

                        foreach (var footerLine in footerLines)
                        {
                            var strippedLine = footerLine.Trim();
                            // Target specific line from template2.xml (around line 7010)
                            if (strippedLine == "<Text>去漆皮部套</Text>")
                            {
                                // Replace the content with value from Excel column C (OpMode注释) read from row 2
                                var originalIndent = footerLine.Substring(0, footerLine.IndexOf("<"));
                                var newLine = $"{originalIndent}<Text>{opModeComment}</Text>";
                                processedFooterLines.Add(newLine);
                                OnLogMessage($"Footer: Replaced '<Text>去漆皮部套</Text>' with '<Text>{opModeComment}</Text>' from Excel Col C (OpMode注释).");
                            }
                            else if (footerLine.Contains("NetworkSource") && footerLine.Contains("FlgNet"))
                            {
                                // Avoid duplicating these closing tags
                                // Do not add if it's the closing tag line that was already handled
                            }
                            else
                            {
                                processedFooterLines.Add(footerLine);
                            }
                        }

                        var footerContent = string.Join(Environment.NewLine, processedFooterLines);

                        // 合并内容
                        // Ensure network_content ends with a newline before appending footer
                        if (!networkContent.EndsWith(Environment.NewLine))
                        {
                            networkContent += Environment.NewLine;
                        }
                        networkContent += footerContent;
                    }
                    else
                    {
                        OnLogMessage("未找到网络结束标记'</FlgNet></NetworkSource>'，无法精确替换末尾部分");
                        // 保持原有内容，避免破坏XML结构
                    }
                }

                // 更新ID以避免冲突 - 确保只更新网络代码部分的ID，完全不修改HMI/mode部分的ID
                networkContent = UpdateNetworkIds(networkContent, i);

                // 将网络内容按行分割并添加到输出
                var networkLines = networkContent.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
                outputLines.AddRange(networkLines);
            }
        }

        /// <summary>
        /// 自定义网络模板中的参数，对应Python代码中的_customize_network_content方法
        /// </summary>
        private string CustomizeNetworkContent(string networkContent, InstanceData instance, int index)
        {
            OnLogMessage($"Customizing network content for instance '{instance.Name}' (Excel row {instance.ExcelRow}) - simplified.");

            // 1. 替换网络标题中的实例注释 (原为实例名称)
            networkContent = System.Text.RegularExpressions.Regex.Replace(networkContent, @"<Text>([^<]+)</Text>", $"<Text>{instance.Comment}</Text>");

            OnLogMessage($"Network content customization complete for '{instance.Name}'.");
            return networkContent;
        }

        // 类静态变量，用于跟踪当前ID值
        private static int _currentGlobalId = 0x13; // 从十六进制13开始

        /// <summary>
        /// 更新网络中的ID属性，为每个实例分配唯一ID，对应Python代码中的_update_network_ids方法
        /// </summary>
        private string UpdateNetworkIds(string networkContent, int index)
        {
            // 在首次运行时重置静态ID计数器
            if (index == 0)
            {
                _currentGlobalId = 0x13; // 从十六进制13开始
            }

            OnLogMessage($"实例 {index + 1} 的ID基础值: 0x{_currentGlobalId:X}");

            // 首先检查是否包含HMI/mode固定内容部分（ID值为1-12）
            // 如果包含，直接返回原内容，不做任何修改
            var hmiModeIdPattern = @"ID=""([1-9]|1[0-2]|[A-F])""";
            if (System.Text.RegularExpressions.Regex.IsMatch(networkContent, hmiModeIdPattern))
            {
                OnLogMessage("检测到HMI/mode固定内容部分，保持原样不修改");
                return networkContent;
            }

            // 创建ID映射字典，用于跟踪已分配的ID
            var idMap = new Dictionary<string, string>();

            // 处理MultilingualText的ID
            networkContent = System.Text.RegularExpressions.Regex.Replace(networkContent, @"<MultilingualText ID=""([^""]+)""", match =>
            {
                var oldId = match.Groups[1].Value;
                // 跳过HMI/mode固定内容部分的ID（1-12）
                if (new[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "10", "11", "12" }.Contains(oldId))
                {
                    return match.Value;
                }

                if (!idMap.ContainsKey(oldId))
                {
                    var newId = _currentGlobalId.ToString("X").ToUpper();
                    idMap[oldId] = newId;
                    OnLogMessage($"[ID_REMAP] 将ID='{oldId}'映射为'{newId}'");
                    _currentGlobalId++;
                }

                return $"<MultilingualText ID=\"{idMap[oldId]}\"";
            });

            // 处理MultilingualTextItem的ID
            networkContent = System.Text.RegularExpressions.Regex.Replace(networkContent, @"<MultilingualTextItem ID=""([^""]+)""", match =>
            {
                var oldId = match.Groups[1].Value;
                // 跳过HMI/mode固定内容部分的ID（1-12）
                if (new[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "10", "11", "12" }.Contains(oldId))
                {
                    return match.Value;
                }

                if (!idMap.ContainsKey(oldId))
                {
                    var newId = _currentGlobalId.ToString("X").ToUpper();
                    idMap[oldId] = newId;
                    OnLogMessage($"[ID_REMAP] 将ID='{oldId}'映射为'{newId}'");
                    _currentGlobalId++;
                }

                return $"<MultilingualTextItem ID=\"{idMap[oldId]}\"";
            });

            // 处理CompileUnit的ID
            networkContent = System.Text.RegularExpressions.Regex.Replace(networkContent, @"<SW\.Blocks\.CompileUnit ID=""([^""]+)""", match =>
            {
                var oldId = match.Groups[1].Value;
                // 跳过HMI/mode固定内容部分的ID（1-12）
                if (new[] { "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "10", "11", "12" }.Contains(oldId))
                {
                    return match.Value;
                }

                if (!idMap.ContainsKey(oldId))
                {
                    var newId = _currentGlobalId.ToString("X").ToUpper();
                    idMap[oldId] = newId;
                    OnLogMessage($"[ID_REMAP] 将ID='{oldId}'映射为'{newId}'");
                    _currentGlobalId++;
                }

                return $"<SW.Blocks.CompileUnit ID=\"{idMap[oldId]}\"";
            });

            OnLogMessage($"实例 {index + 1} 的ID映射完成，最终ID为: 0x{(_currentGlobalId - 1):X}");
            return networkContent;
        }

        /// <summary>
        /// 验证输入文件
        /// </summary>
        private async Task<bool> ValidateInputFilesAsync(GenerationConfig config)
        {
            if (!_fileService.FileExists(config.ExcelFilePath))
            {
                OnLogMessage($"错误：Excel文件不存在：{config.ExcelFilePath}");
                return false;
            }

            if (!_fileService.FileExists(config.TemplateFilePath))
            {
                OnLogMessage($"错误：模板文件不存在：{config.TemplateFilePath}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 生成静态成员变量
        /// </summary>
        private bool GenerateStaticMembers(List<InstanceData> instances)
        {
            try
            {
                OnLogMessage("开始生成静态成员变量");

                // 查找Static段
                var staticSection = FindStaticSection();
                if (staticSection == null)
                {
                    OnLogMessage("错误：未找到Static段");
                    return false;
                }

                // 清空现有内容
                staticSection.RemoveAll();

                // 生成实例声明
                GenerateInstanceDeclarations(staticSection, instances);

                // 生成固定成员
                GenerateFixedMembers(staticSection);

                OnLogMessage($"成功生成静态成员变量（{instances.Count}个实例 + 固定成员）");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成静态成员变量时发生错误");
                OnLogMessage($"生成静态成员变量时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找Static段
        /// </summary>
        private XElement? FindStaticSection()
        {
            if (_rootElement == null) return null;

            return _rootElement
                .Descendants(_xmlNamespace + "Interface")
                .Descendants(_xmlNamespace + "Sections")
                .Descendants(_xmlNamespace + "Section")
                .FirstOrDefault(s => s.Attribute("Name")?.Value == "Static");
        }

        /// <summary>
        /// 生成实例声明
        /// </summary>
        private void GenerateInstanceDeclarations(XElement staticSection, List<InstanceData> instances)
        {
            // 这里需要从模板中读取实例声明模板（66-436行）
            // 然后为每个实例生成相应的声明
            // 具体实现需要根据模板结构来完成
            OnLogMessage("生成实例声明内容");

            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                // TODO: 实现实例声明生成逻辑
                OnLogMessage($"生成实例 '{instance.Name}' 的声明");
            }
        }

        /// <summary>
        /// 生成固定成员
        /// </summary>
        private void GenerateFixedMembers(XElement staticSection)
        {
            // 这里需要从模板中读取固定成员模板（3028-3076行）
            // 然后添加到Static段中
            OnLogMessage("生成固定变量的声明");
        }

        /// <summary>
        /// 生成网络代码
        /// </summary>
        private bool GenerateNetworks(List<InstanceData> instances, OpModeData opModeData)
        {
            try
            {
                OnLogMessage("开始生成网络代码");

                // 使用NetworkGenerator生成网络代码
                var networkLines = _networkGenerator.GenerateAllNetworks(instances, opModeData);

                // 查找NetworkSource元素并更新
                var networkSource = FindNetworkSourceElement();
                if (networkSource == null)
                {
                    OnLogMessage("错误：未找到NetworkSource元素");
                    return false;
                }

                // 更新网络代码
                var networksText = string.Join(Environment.NewLine, networkLines);
                networkSource.Value = networksText;

                OnLogMessage($"成功生成网络代码，共 {networkLines.Count} 行");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成网络代码时发生错误");
                OnLogMessage($"生成网络代码时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找NetworkSource元素
        /// </summary>
        private XElement? FindNetworkSourceElement()
        {
            if (_rootElement == null) return null;

            return _rootElement
                .Descendants(_xmlNamespace + "SW.Blocks.FB")
                .Descendants(_xmlNamespace + "ObjectList")
                .Descendants(_xmlNamespace + "SW.Blocks.CompileUnit")
                .Descendants(_xmlNamespace + "AttributeList")
                .Descendants(_xmlNamespace + "NetworkSource")
                .FirstOrDefault();
        }

        /// <summary>
        /// 修复多语言文本ID冲突
        /// </summary>
        private void FixMultilingualTextIds(bool preserveHmiModeIds = false)
        {
            OnLogMessage("开始修复MultilingualText和相关元素的ID冲突");

            if (_rootElement == null) return;

            // 查找所有带ID属性的元素
            var elementsWithId = _rootElement.Descendants()
                .Where(e => e.Attribute("ID") != null)
                .ToList();

            OnLogMessage($"找到 {elementsWithId.Count} 个带ID属性的元素");

            // 重新分配ID
            var idCounter = 0x13;
            var idMappings = new Dictionary<string, string>();

            foreach (var element in elementsWithId)
            {
                var oldId = element.Attribute("ID")?.Value;
                if (string.IsNullOrEmpty(oldId)) continue;

                // 检查是否需要保留HMI/mode部分的ID
                if (preserveHmiModeIds && ShouldPreserveId(element, oldId))
                    continue;

                if (!idMappings.ContainsKey(oldId))
                {
                    var newId = idCounter.ToString("X");
                    idMappings[oldId] = newId;
                    idCounter++;
                }

                element.SetAttributeValue("ID", idMappings[oldId]);
            }

            OnLogMessage($"成功重映射了 {idMappings.Count} 个ID");
        }

        /// <summary>
        /// 判断是否应该保留ID
        /// </summary>
        private bool ShouldPreserveId(XElement element, string id)
        {
            var elementName = element.Name.LocalName;

            // HMI/mode部分的特定ID
            var preserveIds = new Dictionary<string, string[]>
            {
                ["MultilingualText"] = new[] { "1", "5", "8", "C", "F" },
                ["MultilingualTextItem"] = new[] { "2", "3", "6", "7", "9", "A", "D", "E", "10", "11" },
                ["SW.Blocks.CompileUnit"] = new[] { "4", "B", "12" }
            };

            return preserveIds.TryGetValue(elementName, out var ids) && ids.Contains(id);
        }

        #region 事件触发方法

        private void OnLogMessage(string message)
        {
            _logger.LogInformation(message);
            LogMessage?.Invoke(this, message);
        }

        private void OnProgressChanged(int progress)
        {
            ProgressChanged?.Invoke(this, progress);
        }

        private void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, status);
        }

        #endregion
    }
}
