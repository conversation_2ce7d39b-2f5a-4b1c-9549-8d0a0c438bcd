using Microsoft.Extensions.Logging;
using System.Xml.Linq;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

namespace TiaXmlGenerator.Core
{
    /// <summary>
    /// XML生成器核心类，对应Python中的XMLGenerator类
    /// </summary>
    public class XmlGenerator : IXmlGeneratorService
    {
        private readonly IExcelReaderService _excelReader;
        private readonly INetworkGeneratorService _networkGenerator;
        private readonly IIdGeneratorService _idGenerator;
        private readonly IFileService _fileService;
        private readonly IXmlProcessorService _xmlProcessor;
        private readonly ILogger<XmlGenerator> _logger;

        private XDocument? _xmlDocument;
        private XElement? _rootElement;
        private XNamespace? _xmlNamespace;

        // 实例参数映射，对应Python中的INSTANCE_PARAM_MAPPING
        private static readonly Dictionary<string, ParameterMapping> InstanceParamMapping = new()
        {
            ["forward_sensor"] = new() { Section = "Input", Member = "xSnsFwd", IsBool = false },
            ["backward_sensor"] = new() { Section = "Input", Member = "xSnsBwd", IsBool = false },
            ["forward_output"] = new() { Section = "Output", Member = "xCmdFwd", IsBool = false },
            ["backward_output"] = new() { Section = "Output", Member = "xCmdBwd", IsBool = false },
            ["auto_forward"] = new() { Section = "Input", Member = "bAutoFwd", IsBool = true },
            ["auto_backward"] = new() { Section = "Input", Member = "bAutoBwd", IsBool = true },
            ["forward_permit"] = new() { Section = "Input", Member = "bPermitFwd", IsBool = true },
            ["backward_permit"] = new() { Section = "Input", Member = "bPermitBwd", IsBool = true }
        };

        // Parts修改规则 - 单输入类型
        private static readonly Dictionary<string, PartsModificationRule> PartsModificationsSingleInput = new()
        {
            ["21"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a", NewNameExcelCol = "I点-工作位1" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "I点-工作位1偏移量" }
                }
            },
            ["22"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b", NewNameExcelCol = "I点-原位1" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "I点-原位1偏移量" }
                }
            },
            ["23"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "DB_ControlNodesC5", NewNameExcelCol = "手动行DB块名称" },
                    new() { OriginalName = "em12", NewNameExcelCol = "OpMode名称" },
                    new() { OriginalName = "NA1C101左缓存挡料气缸1", NewNameExcelCol = "name" }
                }
            },
            ["29"] = new()
            {
                Constants = new List<ConstantRule>
                {
                    new() { OriginalValue = "12", NewValueExcelCol = "OpMode编号" }
                }
            },
            ["35"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "Q216.4 NA1C101挡料气缸1伸出", NewNameExcelCol = "Q点-工作位" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "Q点-工作位偏移量" }
                }
            },
            ["36"] = new()
            {
                Components = new List<ComponentRule>
                {
                    new() { OriginalName = "Q216.5 NA1C101挡料气缸1缩回", NewNameExcelCol = "Q点-原位" }
                },
                Addresses = new List<AddressRule>
                {
                    new() { NewBitOffsetExcelCol = "Q点-原位偏移量" }
                }
            }
        };

        public event EventHandler<string>? LogMessage;
        public event EventHandler<int>? ProgressChanged;
        public event EventHandler<string>? StatusChanged;

        public XmlGenerator(
            IExcelReaderService excelReader,
            INetworkGeneratorService networkGenerator,
            IIdGeneratorService idGenerator,
            IFileService fileService,
            IXmlProcessorService xmlProcessor,
            ILogger<XmlGenerator> logger)
        {
            _excelReader = excelReader;
            _networkGenerator = networkGenerator;
            _idGenerator = idGenerator;
            _fileService = fileService;
            _xmlProcessor = xmlProcessor;
            _logger = logger;
        }

        /// <summary>
        /// 生成XML文件
        /// </summary>
        public async Task<bool> GenerateXmlAsync(GenerationConfig config)
        {
            try
            {
                OnStatusChanged("开始生成XML文件...");
                OnProgressChanged(0);

                // 验证输入文件
                if (!await ValidateInputFilesAsync(config))
                    return false;

                OnProgressChanged(10);

                // 读取Excel数据
                OnStatusChanged("读取Excel数据...");
                var excelResult = await _excelReader.ReadExcelDataAsync(config.ExcelFilePath);
                if (!excelResult.Instances.Any())
                {
                    OnLogMessage("错误：未能从Excel文件中读取到有效数据");
                    return false;
                }

                OnLogMessage($"成功读取 {excelResult.Instances.Count} 个实例数据");
                OnProgressChanged(30);

                // 加载模板XML
                OnStatusChanged("加载模板XML...");
                _xmlDocument = await _xmlProcessor.LoadXmlDocumentAsync(config.TemplateFilePath);
                if (_xmlDocument?.Root == null)
                {
                    OnLogMessage("错误：无法加载模板XML文件");
                    return false;
                }

                _rootElement = _xmlDocument.Root;
                _xmlNamespace = _rootElement.GetDefaultNamespace();
                OnProgressChanged(40);

                // 生成静态成员
                OnStatusChanged("生成静态成员变量...");
                if (!GenerateStaticMembers(excelResult.Instances))
                {
                    OnLogMessage("错误：生成静态成员变量失败");
                    return false;
                }
                OnProgressChanged(60);

                // 生成网络代码
                OnStatusChanged("生成网络代码...");
                if (!GenerateNetworks(excelResult.Instances, new OpModeData()))
                {
                    OnLogMessage("错误：生成网络代码失败");
                    return false;
                }
                OnProgressChanged(80);

                // 修复ID冲突
                OnStatusChanged("修复ID冲突...");
                FixMultilingualTextIds();
                OnProgressChanged(90);

                // 保存XML文件
                OnStatusChanged("保存XML文件...");
                await _xmlProcessor.SaveXmlDocumentAsync(_xmlDocument, config.OutputFilePath);
                OnProgressChanged(100);

                OnStatusChanged("XML生成完成");
                OnLogMessage($"XML文件已成功生成：{config.OutputFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成XML时发生错误");
                OnLogMessage($"生成XML时发生错误：{ex.Message}");
                OnStatusChanged("生成失败");
                return false;
            }
        }

        /// <summary>
        /// 验证输入文件
        /// </summary>
        private async Task<bool> ValidateInputFilesAsync(GenerationConfig config)
        {
            if (!_fileService.FileExists(config.ExcelFilePath))
            {
                OnLogMessage($"错误：Excel文件不存在：{config.ExcelFilePath}");
                return false;
            }

            if (!_fileService.FileExists(config.TemplateFilePath))
            {
                OnLogMessage($"错误：模板文件不存在：{config.TemplateFilePath}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 生成静态成员变量
        /// </summary>
        private bool GenerateStaticMembers(List<InstanceData> instances)
        {
            try
            {
                OnLogMessage("开始生成静态成员变量");

                // 查找Static段
                var staticSection = FindStaticSection();
                if (staticSection == null)
                {
                    OnLogMessage("错误：未找到Static段");
                    return false;
                }

                // 清空现有内容
                staticSection.RemoveAll();

                // 生成实例声明
                GenerateInstanceDeclarations(staticSection, instances);

                // 生成固定成员
                GenerateFixedMembers(staticSection);

                OnLogMessage($"成功生成静态成员变量（{instances.Count}个实例 + 固定成员）");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成静态成员变量时发生错误");
                OnLogMessage($"生成静态成员变量时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找Static段
        /// </summary>
        private XElement? FindStaticSection()
        {
            if (_rootElement == null) return null;

            return _rootElement
                .Descendants(_xmlNamespace + "Interface")
                .Descendants(_xmlNamespace + "Sections")
                .Descendants(_xmlNamespace + "Section")
                .FirstOrDefault(s => s.Attribute("Name")?.Value == "Static");
        }

        /// <summary>
        /// 生成实例声明
        /// </summary>
        private void GenerateInstanceDeclarations(XElement staticSection, List<InstanceData> instances)
        {
            // 这里需要从模板中读取实例声明模板（66-436行）
            // 然后为每个实例生成相应的声明
            // 具体实现需要根据模板结构来完成
            OnLogMessage("生成实例声明内容");
            
            for (int i = 0; i < instances.Count; i++)
            {
                var instance = instances[i];
                // TODO: 实现实例声明生成逻辑
                OnLogMessage($"生成实例 '{instance.Name}' 的声明");
            }
        }

        /// <summary>
        /// 生成固定成员
        /// </summary>
        private void GenerateFixedMembers(XElement staticSection)
        {
            // 这里需要从模板中读取固定成员模板（3028-3076行）
            // 然后添加到Static段中
            OnLogMessage("生成固定变量的声明");
        }

        /// <summary>
        /// 生成网络代码
        /// </summary>
        private bool GenerateNetworks(List<InstanceData> instances, OpModeData opModeData)
        {
            try
            {
                OnLogMessage("开始生成网络代码");

                // 使用NetworkGenerator生成网络代码
                var networkLines = _networkGenerator.GenerateAllNetworks(instances, opModeData);

                // 查找NetworkSource元素并更新
                var networkSource = FindNetworkSourceElement();
                if (networkSource == null)
                {
                    OnLogMessage("错误：未找到NetworkSource元素");
                    return false;
                }

                // 更新网络代码
                var networksText = string.Join(Environment.NewLine, networkLines);
                networkSource.Value = networksText;

                OnLogMessage($"成功生成网络代码，共 {networkLines.Count} 行");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成网络代码时发生错误");
                OnLogMessage($"生成网络代码时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找NetworkSource元素
        /// </summary>
        private XElement? FindNetworkSourceElement()
        {
            if (_rootElement == null) return null;

            return _rootElement
                .Descendants(_xmlNamespace + "SW.Blocks.FB")
                .Descendants(_xmlNamespace + "ObjectList")
                .Descendants(_xmlNamespace + "SW.Blocks.CompileUnit")
                .Descendants(_xmlNamespace + "AttributeList")
                .Descendants(_xmlNamespace + "NetworkSource")
                .FirstOrDefault();
        }

        /// <summary>
        /// 修复多语言文本ID冲突
        /// </summary>
        private void FixMultilingualTextIds(bool preserveHmiModeIds = false)
        {
            OnLogMessage("开始修复MultilingualText和相关元素的ID冲突");

            if (_rootElement == null) return;

            // 查找所有带ID属性的元素
            var elementsWithId = _rootElement.Descendants()
                .Where(e => e.Attribute("ID") != null)
                .ToList();

            OnLogMessage($"找到 {elementsWithId.Count} 个带ID属性的元素");

            // 重新分配ID
            var idCounter = 0x13;
            var idMappings = new Dictionary<string, string>();

            foreach (var element in elementsWithId)
            {
                var oldId = element.Attribute("ID")?.Value;
                if (string.IsNullOrEmpty(oldId)) continue;

                // 检查是否需要保留HMI/mode部分的ID
                if (preserveHmiModeIds && ShouldPreserveId(element, oldId))
                    continue;

                if (!idMappings.ContainsKey(oldId))
                {
                    var newId = idCounter.ToString("X");
                    idMappings[oldId] = newId;
                    idCounter++;
                }

                element.SetAttributeValue("ID", idMappings[oldId]);
            }

            OnLogMessage($"成功重映射了 {idMappings.Count} 个ID");
        }

        /// <summary>
        /// 判断是否应该保留ID
        /// </summary>
        private bool ShouldPreserveId(XElement element, string id)
        {
            var elementName = element.Name.LocalName;
            
            // HMI/mode部分的特定ID
            var preserveIds = new Dictionary<string, string[]>
            {
                ["MultilingualText"] = new[] { "1", "5", "8", "C", "F" },
                ["MultilingualTextItem"] = new[] { "2", "3", "6", "7", "9", "A", "D", "E", "10", "11" },
                ["SW.Blocks.CompileUnit"] = new[] { "4", "B", "12" }
            };

            return preserveIds.TryGetValue(elementName, out var ids) && ids.Contains(id);
        }

        #region 事件触发方法

        private void OnLogMessage(string message)
        {
            _logger.LogInformation(message);
            LogMessage?.Invoke(this, message);
        }

        private void OnProgressChanged(int progress)
        {
            ProgressChanged?.Invoke(this, progress);
        }

        private void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, status);
        }

        #endregion
    }
}
