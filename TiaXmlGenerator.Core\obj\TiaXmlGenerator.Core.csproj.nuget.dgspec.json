{"format": 1, "restore": {"D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Core\\TiaXmlGenerator.Core.csproj": {}}, "projects": {"D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Core\\TiaXmlGenerator.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Core\\TiaXmlGenerator.Core.csproj", "projectName": "TiaXmlGenerator.Core", "projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Core\\TiaXmlGenerator.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj": {"projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj"}, "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\TiaXmlGenerator.Services.csproj": {"projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\TiaXmlGenerator.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.Xml.XDocument": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj", "projectName": "TiaXmlGenerator.Models", "projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\TiaXmlGenerator.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\TiaXmlGenerator.Services.csproj", "projectName": "TiaXmlGenerator.Services", "projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\TiaXmlGenerator.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj": {"projectPath": "D:\\桌面\\develop\\TIA_Openness\\GenXML_C#\\TiaXmlGenerator.Models\\TiaXmlGenerator.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}