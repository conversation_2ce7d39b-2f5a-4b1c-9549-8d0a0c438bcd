# TIA XML Generator C# 项目架构规划

## 1. 解决方案结构

```
TiaXmlGenerator.sln
├── TiaXmlGenerator.Core/              # 核心业务逻辑类库
├── TiaXmlGenerator.UI/                # WPF用户界面
├── TiaXmlGenerator.Models/            # 数据模型
├── TiaXmlGenerator.Services/          # 服务层
├── TiaXmlGenerator.Tests/             # 单元测试
└── TiaXmlGenerator.Console/           # 控制台版本（可选）
```

## 2. 项目详细设计

### 2.1 TiaXmlGenerator.Models

**数据模型和配置类**

```csharp
// Models/InstanceData.cs
public class InstanceData
{
    public string Name { get; set; }
    public string Comment { get; set; }
    public int ExcelRow { get; set; }
    public bool IsDoubleInputType { get; set; }
    public string Type { get; set; }
    
    // I/O 配置
    public string ForwardSensorAddr { get; set; }
    public string BackwardSensorAddr { get; set; }
    public string ForwardOutputAddr { get; set; }
    public string BackwardOutputAddr { get; set; }
    
    // 双输入类型的额外配置
    public string ForwardSensor2Addr { get; set; }
    public string BackwardSensor2Addr { get; set; }
    
    // 偏移量配置
    public string ForwardSensorOffset { get; set; }
    public string BackwardSensorOffset { get; set; }
    public string ForwardOutputOffset { get; set; }
    public string BackwardOutputOffset { get; set; }
    
    // 其他配置
    public string ManualDbName { get; set; }
    public string OpModeName { get; set; }
    public string OpModeNumber { get; set; }
    public string FbTemplate { get; set; }
}

// Models/OpModeData.cs
public class OpModeData
{
    public string FbName { get; set; }
    public string OpModeName { get; set; }
    public string OpModeNumber { get; set; }
    public string OpModeComment { get; set; }
    public int FbNumber { get; set; }
}

// Models/GenerationConfig.cs
public class GenerationConfig
{
    public string ExcelFilePath { get; set; }
    public string TemplateFilePath { get; set; }
    public string OutputFilePath { get; set; }
    public int FbInstanceSize { get; set; } = 256;
}

// Models/ParameterMapping.cs
public class ParameterMapping
{
    public string Section { get; set; }
    public string Member { get; set; }
    public bool IsBool { get; set; }
}

// Models/PartsModificationRule.cs
public class PartsModificationRule
{
    public List<ComponentRule> Components { get; set; } = new();
    public List<AddressRule> Addresses { get; set; } = new();
    public List<ConstantRule> Constants { get; set; } = new();
}

public class ComponentRule
{
    public string OriginalName { get; set; }
    public string NewNameExcelCol { get; set; }
}

public class AddressRule
{
    public string NewBitOffsetExcelCol { get; set; }
}

public class ConstantRule
{
    public string OriginalValue { get; set; }
    public string NewValueExcelCol { get; set; }
}
```

### 2.2 TiaXmlGenerator.Services

**服务层接口和实现**

```csharp
// Services/Interfaces/IExcelReaderService.cs
public interface IExcelReaderService
{
    Task<(List<InstanceData> instances, Dictionary<string, object> uidParamMap, string opModeComment)> 
        ReadExcelDataAsync(string filePath);
}

// Services/Interfaces/IXmlGeneratorService.cs
public interface IXmlGeneratorService
{
    Task<bool> GenerateXmlAsync(GenerationConfig config);
    event EventHandler<string> LogMessage;
    event EventHandler<int> ProgressChanged;
}

// Services/Interfaces/INetworkGeneratorService.cs
public interface INetworkGeneratorService
{
    List<string> GenerateAllNetworks(List<InstanceData> instances, OpModeData opModeData);
    List<string> GenerateFixedNetworksPart();
    List<string> GenerateInstanceNetwork(InstanceData instanceData, OpModeData opModeData, bool isLastInstance = false);
}

// Services/Interfaces/IIdGeneratorService.cs
public interface IIdGeneratorService
{
    string GetNextId();
    void Reset(int startId = 0x13);
}
```

### 2.3 TiaXmlGenerator.Core

**核心业务逻辑**

```csharp
// Core/XmlGenerator.cs
public class XmlGenerator : IXmlGeneratorService
{
    private readonly IExcelReaderService _excelReader;
    private readonly INetworkGeneratorService _networkGenerator;
    private readonly IIdGeneratorService _idGenerator;
    private readonly ILogger<XmlGenerator> _logger;
    
    public event EventHandler<string> LogMessage;
    public event EventHandler<int> ProgressChanged;
    
    public XmlGenerator(
        IExcelReaderService excelReader,
        INetworkGeneratorService networkGenerator,
        IIdGeneratorService idGenerator,
        ILogger<XmlGenerator> logger)
    {
        _excelReader = excelReader;
        _networkGenerator = networkGenerator;
        _idGenerator = idGenerator;
        _logger = logger;
    }
    
    public async Task<bool> GenerateXmlAsync(GenerationConfig config)
    {
        // 实现XML生成逻辑
    }
    
    private void GenerateStaticMembers() { }
    private void GenerateInstanceMembers() { }
    private void GenerateNetworks() { }
    private void FixMultilingualTextIds() { }
}

// Core/NetworkGenerator.cs
public class NetworkGenerator : INetworkGeneratorService
{
    private readonly IIdGeneratorService _idGenerator;
    private readonly ILogger<NetworkGenerator> _logger;
    private readonly string _templateFilePath;
    
    // 实现网络生成逻辑
}

// Core/IdGenerator.cs
public class IdGenerator : IIdGeneratorService
{
    private int _currentId;
    
    public IdGenerator(int startId = 0x13)
    {
        _currentId = startId;
    }
    
    public string GetNextId()
    {
        var nextId = _currentId;
        _currentId++;
        return nextId.ToString("X");
    }
    
    public void Reset(int startId = 0x13)
    {
        _currentId = startId;
    }
}
```

### 2.4 TiaXmlGenerator.UI (WPF界面)

**MVVM模式的WPF应用**

```csharp
// ViewModels/MainViewModel.cs
public class MainViewModel : ViewModelBase
{
    private readonly IXmlGeneratorService _xmlGenerator;
    private string _excelFilePath;
    private string _templateFilePath;
    private string _outputFilePath;
    private int _progressValue;
    private string _statusMessage;
    private ObservableCollection<string> _logMessages;
    
    public ICommand BrowseExcelCommand { get; }
    public ICommand BrowseTemplateCommand { get; }
    public ICommand BrowseOutputCommand { get; }
    public ICommand GenerateXmlCommand { get; }
    
    // 属性和命令实现
}

// Views/MainWindow.xaml
<Window x:Class="TiaXmlGenerator.UI.Views.MainWindow">
    <!-- WPF界面设计 -->
</Window>

// App.xaml.cs
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 配置依赖注入
        var serviceCollection = new ServiceCollection();
        ConfigureServices(serviceCollection);
        
        var serviceProvider = serviceCollection.BuildServiceProvider();
        var mainWindow = serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }
    
    private void ConfigureServices(IServiceCollection services)
    {
        // 注册服务
        services.AddSingleton<IIdGeneratorService, IdGenerator>();
        services.AddScoped<IExcelReaderService, ExcelReaderService>();
        services.AddScoped<INetworkGeneratorService, NetworkGenerator>();
        services.AddScoped<IXmlGeneratorService, XmlGenerator>();
        services.AddTransient<MainViewModel>();
        services.AddTransient<MainWindow>();
        
        // 配置日志
        services.AddLogging(builder => builder.AddConsole());
    }
}
```

## 3. 技术栈选择

### 3.1 核心框架
- **.NET 6/7/8**: 现代.NET平台
- **WPF**: 桌面UI框架
- **MVVM Pattern**: UI架构模式

### 3.2 第三方库
- **EPPlus**: Excel文件读写 (替代openpyxl)
- **System.Xml.Linq**: XML处理 (替代lxml)
- **Microsoft.Extensions.DependencyInjection**: 依赖注入
- **Microsoft.Extensions.Logging**: 日志框架
- **CommunityToolkit.Mvvm**: MVVM辅助库

### 3.3 项目文件配置

```xml
<!-- TiaXmlGenerator.Core.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
  </ItemGroup>
</Project>

<!-- TiaXmlGenerator.UI.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\TiaXmlGenerator.Core\TiaXmlGenerator.Core.csproj" />
    <ProjectReference Include="..\TiaXmlGenerator.Models\TiaXmlGenerator.Models.csproj" />
    <ProjectReference Include="..\TiaXmlGenerator.Services\TiaXmlGenerator.Services.csproj" />
  </ItemGroup>
</Project>
```

## 4. 迁移策略

### 4.1 阶段性迁移
1. **第一阶段**: 创建项目结构和数据模型
2. **第二阶段**: 实现Excel读取服务
3. **第三阶段**: 实现XML生成核心逻辑
4. **第四阶段**: 实现网络生成器
5. **第五阶段**: 创建WPF用户界面
6. **第六阶段**: 测试和优化

### 4.2 Python到C#的主要转换点
- **字典 → Dictionary<TKey, TValue>**
- **列表 → List<T>**
- **字符串处理 → String methods + Regex**
- **文件I/O → File/Stream APIs**
- **异常处理 → try-catch-finally**
- **日志 → ILogger接口**
- **异步处理 → async/await**

这个架构保持了原有Python项目的设计思路，同时充分利用了C#和.NET的特性，提供了更好的类型安全性、性能和可维护性。
