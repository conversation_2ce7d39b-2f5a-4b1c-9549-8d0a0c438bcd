import openpyxl
from openpyxl import Workbook

# 创建一个新的工作簿
wb = Workbook()
ws = wb.active

# 设置表头
headers = [
    "实例名称", "实例注释", "I点-工作位1", "I点-原位1", 
    "Q点-工作位", "Q点-原位", "OpMode注释"
]

# 写入表头
for col, header in enumerate(headers, 1):
    ws.cell(row=1, column=col, value=header)

# 写入测试数据
test_data = [
    ["测试气缸1", "测试气缸1注释", "I132.0", "I132.1", "Q216.4", "Q216.5", "测试OpMode注释"],
    ["测试气缸2", "测试气缸2注释", "I133.0", "I133.1", "Q217.4", "Q217.5", ""],
    ["测试气缸3", "测试气缸3注释", "I134.0", "I134.1", "Q218.4", "Q218.5", ""]
]

for row_idx, row_data in enumerate(test_data, 2):
    for col_idx, value in enumerate(row_data, 1):
        ws.cell(row=row_idx, column=col_idx, value=value)

# 保存文件
wb.save("test_data.xlsx")
print("测试Excel文件已创建: test_data.xlsx")

# 也创建一个manualRow_2.xlsx文件
wb.save("manualRow_2.xlsx")
print("测试Excel文件已创建: manualRow_2.xlsx")
