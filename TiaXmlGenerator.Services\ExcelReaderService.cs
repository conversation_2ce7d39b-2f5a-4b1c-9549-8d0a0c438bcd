using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

// 全局设置EPPlus许可证（非商业用途）
[assembly: System.Runtime.CompilerServices.InternalsVisibleTo("EPPlus")]

namespace TiaXmlGenerator.Services
{
    /// <summary>
    /// Excel读取服务，对应Python中的read_excel_data方法
    /// </summary>
    public class ExcelReaderService : IExcelReaderService
    {
        private readonly ILogger<ExcelReaderService> _logger;



        public ExcelReaderService(ILogger<ExcelReaderService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 创建ExcelPackage实例，处理EPPlus 8.0许可证问题
        /// </summary>
        private ExcelPackage CreateExcelPackage(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                _logger.LogInformation("Excel文件信息: 路径={Path}, 大小={Size}字节, 存在={Exists}",
                    filePath, fileInfo.Length, fileInfo.Exists);

                // 许可证应该已经在应用程序启动时设置，直接创建ExcelPackage
                var package = new ExcelPackage(fileInfo);
                _logger.LogInformation("Excel包创建成功，工作表数量: {WorksheetCount}", package.Workbook.Worksheets.Count);
                return package;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建Excel包时发生错误: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 读取Excel数据 - 严格按照Python代码的逻辑实现
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>读取结果</returns>
        public async Task<ExcelReadResult> ReadExcelDataAsync(string filePath)
        {
            var instances = new List<InstanceData>();
            var uidParamMap = new Dictionary<string, object>();
            var opModeCommentForFooter = string.Empty;

            try
            {
                _logger.LogInformation("=== 开始Excel读取过程 ===");
                _logger.LogInformation("目标文件路径: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogError("Excel文件不存在: {FilePath}", filePath);
                    _logger.LogError("请检查文件路径是否正确");
                    return new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter };
                }

                var fileInfo = new FileInfo(filePath);
                _logger.LogInformation("Excel文件信息: 大小={Size}字节, 最后修改时间={LastWrite}",
                    fileInfo.Length, fileInfo.LastWriteTime);

                _logger.LogInformation("正在创建Excel包...");
                using var package = CreateExcelPackage(filePath);

                _logger.LogInformation("Excel包创建成功，工作簿包含 {Count} 个工作表", package.Workbook.Worksheets.Count);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    _logger.LogError("Excel文件中没有找到活动工作表");
                    _logger.LogError("请确保Excel文件包含至少一个工作表");
                    return new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter };
                }

                _logger.LogInformation("使用工作表: '{WorksheetName}'", worksheet.Name);

                // 检查工作表维度
                if (worksheet.Dimension == null)
                {
                    _logger.LogError("工作表没有数据 (Dimension为null)");
                    _logger.LogError("请确保Excel文件包含数据");
                    return new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter };
                }

                _logger.LogInformation("工作表维度: 行数={Rows}, 列数={Columns}",
                    worksheet.Dimension.Rows, worksheet.Dimension.Columns);

                // 读取表头 - 完全按照Python代码的逻辑
                _logger.LogInformation("正在读取表头...");
                var headers = new List<string>();
                var maxColumn = worksheet.Dimension?.Columns ?? 0;

                _logger.LogInformation("最大列数: {MaxColumn}", maxColumn);

                for (int i = 1; i <= maxColumn; i++)
                {
                    var cellValue = worksheet.Cells[1, i].Value;
                    var headerValue = GetCellValueAsString(cellValue);
                    headers.Add(headerValue);
                    _logger.LogDebug("列{Column}: '{Header}' (原始值: '{RawValue}')", i, headerValue, cellValue);
                }

                _logger.LogInformation("读取到 {Count} 个表头列", headers.Count);
                _logger.LogInformation("Excel表头: [{Headers}]", string.Join(", ", headers.Select((h, i) => $"{i+1}:{h}")));

                // 检查Excel表头中是否存在预期的列 - 根据实际Excel文件更新
                _logger.LogInformation("正在验证必需的列...");
                var expectedColumns = new[]
                {
                    "实例名称", "实例注释",
                    "I点-工作位1", "I点-原位1",
                    "Q点-工作位", "Q点-原位"
                };

                _logger.LogInformation("期望的列: [{ExpectedColumns}]", string.Join(", ", expectedColumns));

                // 验证所需列是否存在
                var missingColumns = new List<string>();
                var foundColumns = new List<string>();

                foreach (var col in expectedColumns)
                {
                    if (!headers.Contains(col))
                    {
                        missingColumns.Add(col);
                        _logger.LogWarning("缺少必需列: '{Column}'", col);
                    }
                    else
                    {
                        foundColumns.Add(col);
                        var index = headers.IndexOf(col) + 1;
                        _logger.LogInformation("找到必需列: '{Column}' (位置: {Index})", col, index);
                    }
                }

                _logger.LogInformation("找到的必需列: [{FoundColumns}]", string.Join(", ", foundColumns));

                if (missingColumns.Any())
                {
                    _logger.LogError("Excel表头中缺少一个或多个必需列: {MissingColumns}", string.Join(", ", missingColumns));
                    _logger.LogError("当前表头包含的列: [{CurrentHeaders}]", string.Join(", ", headers.Where(h => !string.IsNullOrWhiteSpace(h))));
                    _logger.LogError("请确保Excel文件包含所有必需的列");
                    return new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter };
                }

                _logger.LogInformation("列验证通过，所有必需列都存在");

                // 获取列索引 - 完全按照Python代码
                var nameCol = headers.IndexOf("实例名称") + 1;
                var commentCol = headers.IndexOf("实例注释") + 1;

                // I/O 地址列
                var fwdSensorAddrCol = headers.IndexOf("I点-工作位1") + 1;
                var bwdSensorAddrCol = headers.IndexOf("I点-原位1") + 1;
                var fwdOutputAddrCol = headers.IndexOf("Q点-工作位") + 1;
                var bwdOutputAddrCol = headers.IndexOf("Q点-原位") + 1;

                // 可选列偏移量
                var fwdSensorOffsetCol = headers.Contains("I点-工作位1偏移量") ? headers.IndexOf("I点-工作位1偏移量") + 1 : -1;
                var bwdSensorOffsetCol = headers.Contains("I点-原位1偏移量") ? headers.IndexOf("I点-原位1偏移量") + 1 : -1;
                var fwdOutputOffsetCol = headers.Contains("Q点-工作位偏移量") ? headers.IndexOf("Q点-工作位偏移量") + 1 : -1;
                var bwdOutputOffsetCol = headers.Contains("Q点-原位偏移量") ? headers.IndexOf("Q点-原位偏移量") + 1 : -1;

                // 双输入点的可选列
                var fwdSensor2Col = headers.Contains("I点-工作位2") ? headers.IndexOf("I点-工作位2") + 1 : -1;
                var bwdSensor2Col = headers.Contains("I点-原位2") ? headers.IndexOf("I点-原位2") + 1 : -1;
                var fwdSensor2OffsetCol = headers.Contains("I点-工作位2偏移量") ? headers.IndexOf("I点-工作位2偏移量") + 1 : -1;
                var bwdSensor2OffsetCol = headers.Contains("I点-原位2偏移量") ? headers.IndexOf("I点-原位2偏移量") + 1 : -1;

                // 其他可能的列
                var dbNameCol = headers.Contains("手动行DB块名称") ? headers.IndexOf("手动行DB块名称") + 1 : -1;
                var opModeNameCol = headers.Contains("OpMode名称") ? headers.IndexOf("OpMode名称") + 1 : -1;
                var opModeNumberCol = headers.Contains("OpMode编号") ? headers.IndexOf("OpMode编号") + 1 : -1;
                var moduleNameColCHeader = "OpMode注释";
                var moduleNameColC = headers.Contains(moduleNameColCHeader) ? headers.IndexOf(moduleNameColCHeader) + 1 : -1;

                // FB模板编号
                var fbTemplateCol = headers.Contains("手动FB 块编号") ? headers.IndexOf("手动FB 块编号") + 1 : -1;

                // Read the OpMode注释 from the first data row (e.g., row 2) for the footer
                var maxRow = worksheet.Dimension?.Rows ?? 0;
                if (moduleNameColC != -1 && maxRow >= 2)
                {
                    opModeCommentForFooter = GetCellValueAsString(worksheet.Cells[2, moduleNameColC].Value);
                    _logger.LogInformation("Read '{Header}' for footer from Excel row 2: '{Comment}'", moduleNameColCHeader, opModeCommentForFooter);
                }
                else if (moduleNameColC == -1)
                {
                    _logger.LogWarning("Excel column '{Header}' (expected for Column C) not found. Footer text replacement will use an empty string.", moduleNameColCHeader);
                }
                else
                {
                    _logger.LogWarning("Excel column '{Header}' found, but sheet has less than 2 rows. Cannot read footer comment.", moduleNameColCHeader);
                }

                var firstProcessedRowDbName = string.Empty;
                var firstProcessedRowOpModeName = string.Empty;
                var isFirstProcessedRow = true;

                // 读取实例数据 - 完全按照Python代码
                _logger.LogInformation("开始读取实例数据，数据行范围: 2 到 {MaxRow}", maxRow);
                var processedInstanceCount = 0;

                for (int currentRowIdx = 2; currentRowIdx <= maxRow; currentRowIdx++)
                {
                    _logger.LogDebug("正在处理第 {Row} 行", currentRowIdx);

                    var instanceName = GetCellValueAsString(worksheet.Cells[currentRowIdx, nameCol].Value);
                    var instanceComment = GetCellValueAsString(worksheet.Cells[currentRowIdx, commentCol].Value);

                    _logger.LogDebug("第{Row}行数据: 实例名称='{Name}', 实例注释='{Comment}'",
                        currentRowIdx, instanceName, instanceComment);

                    // 如果实例名称为空则跳过
                    if (string.IsNullOrWhiteSpace(instanceName))
                    {
                        _logger.LogDebug("第{Row}行实例名称为空，跳过此行", currentRowIdx);
                        continue;
                    }

                    // 清洁字符串值
                    var instanceNameCleaned = CleanStringQuotes(instanceName);
                    var instanceCommentCleaned = CleanStringQuotes(instanceComment);

                    // 读取基本地址数据
                    var fwdSensorAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensorAddrCol].Value);
                    var bwdSensorAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensorAddrCol].Value);
                    var fwdOutputAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdOutputAddrCol].Value);
                    var bwdOutputAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdOutputAddrCol].Value);

                    // 创建实例数据对象
                    var instance = new InstanceData
                    {
                        Name = instanceNameCleaned,
                        Comment = instanceCommentCleaned,
                        ExcelRow = currentRowIdx,
                        ForwardSensorAddr = fwdSensorAddr,
                        BackwardSensorAddr = bwdSensorAddr,
                        ForwardOutputAddr = fwdOutputAddr,
                        BackwardOutputAddr = bwdOutputAddr,
                        ForwardSensorDesc = $"{fwdSensorAddr} {instanceNameCleaned}工作位",
                        BackwardSensorDesc = $"{bwdSensorAddr} {instanceNameCleaned}原位",
                        ForwardOutputDesc = $"{fwdOutputAddr} {instanceNameCleaned}工作位输出",
                        BackwardOutputDesc = $"{bwdOutputAddr} {instanceNameCleaned}原位输出"
                    };

                    // 存储Excel原始列数据
                    instance.SetExcelValue("I点-工作位1", fwdSensorAddr);
                    instance.SetExcelValue("I点-原位1", bwdSensorAddr);
                    instance.SetExcelValue("Q点-工作位", fwdOutputAddr);
                    instance.SetExcelValue("Q点-原位", bwdOutputAddr);

                    // 添加偏移量数据（如果存在）
                    if (fwdSensorOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensorOffsetCol].Value);
                        instance.SetExcelValue("I点-工作位1偏移量", value);
                        instance.ForwardSensorOffset = value;
                    }
                    if (bwdSensorOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensorOffsetCol].Value);
                        instance.SetExcelValue("I点-原位1偏移量", value);
                        instance.BackwardSensorOffset = value;
                    }
                    if (fwdOutputOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdOutputOffsetCol].Value);
                        instance.SetExcelValue("Q点-工作位偏移量", value);
                        instance.ForwardOutputOffset = value;
                    }
                    if (bwdOutputOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdOutputOffsetCol].Value);
                        instance.SetExcelValue("Q点-原位偏移量", value);
                        instance.BackwardOutputOffset = value;
                    }

                    // 添加双输入点数据（如果存在）
                    var isDoubleInput = false;
                    if (fwdSensor2Col != -1)
                    {
                        var fwdSensor2 = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensor2Col].Value);
                        if (!string.IsNullOrWhiteSpace(fwdSensor2))
                        {
                            instance.SetExcelValue("I点-工作位2", fwdSensor2);
                            instance.ForwardSensor2Addr = fwdSensor2;
                            instance.ForwardSensor2Desc = $"{fwdSensor2} {instanceNameCleaned}工作位2";
                            isDoubleInput = true;
                        }
                    }

                    if (bwdSensor2Col != -1)
                    {
                        var bwdSensor2 = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensor2Col].Value);
                        if (!string.IsNullOrWhiteSpace(bwdSensor2))
                        {
                            instance.SetExcelValue("I点-原位2", bwdSensor2);
                            instance.BackwardSensor2Addr = bwdSensor2;
                            instance.BackwardSensor2Desc = $"{bwdSensor2} {instanceNameCleaned}原位2";
                            isDoubleInput = true;
                        }
                    }

                    // 添加双输入点偏移量（如果存在）
                    if (fwdSensor2OffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensor2OffsetCol].Value);
                        instance.SetExcelValue("I点-工作位2偏移量", value);
                        instance.ForwardSensor2Offset = value;
                    }
                    if (bwdSensor2OffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensor2OffsetCol].Value);
                        instance.SetExcelValue("I点-原位2偏移量", value);
                        instance.BackwardSensor2Offset = value;
                    }

                    // 设置实例类型
                    instance.IsDoubleInputType = isDoubleInput;
                    instance.Type = isDoubleInput ? "DoubleInput" : "SingleInput";

                    // 添加其他可能的数据
                    if (dbNameCol != -1)
                    {
                        var currentDbVal = GetCellValueAsString(worksheet.Cells[currentRowIdx, dbNameCol].Value);
                        if (isFirstProcessedRow && !string.IsNullOrWhiteSpace(currentDbVal))
                            firstProcessedRowDbName = currentDbVal;
                        var dbName = !string.IsNullOrWhiteSpace(currentDbVal) ? currentDbVal : firstProcessedRowDbName;
                        instance.SetExcelValue("手动行DB块名称", dbName);
                        instance.ManualDbName = dbName;
                    }

                    if (opModeNameCol != -1)
                    {
                        var currentOpModeVal = GetCellValueAsString(worksheet.Cells[currentRowIdx, opModeNameCol].Value);
                        if (isFirstProcessedRow && !string.IsNullOrWhiteSpace(currentOpModeVal))
                            firstProcessedRowOpModeName = currentOpModeVal;
                        var opModeName = !string.IsNullOrWhiteSpace(currentOpModeVal) ? currentOpModeVal : firstProcessedRowOpModeName;
                        instance.SetExcelValue("OpMode名称", opModeName);
                        instance.OpModeName = opModeName;
                    }

                    if (opModeNumberCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, opModeNumberCol].Value);
                        instance.SetExcelValue("OpMode编号", value);
                        instance.OpModeNumber = value;
                    }

                    // 获取FB模板编号（如果存在）
                    if (fbTemplateCol != -1)
                    {
                        var fbTemplateName = GetCellValueAsString(worksheet.Cells[currentRowIdx, fbTemplateCol].Value);
                        instance.FbTemplate = fbTemplateName;
                    }

                    instances.Add(instance);
                    processedInstanceCount++;

                    // 使用实例名称作为 uid_param_map 的键
                    uidParamMap[instanceNameCleaned] = instance;

                    _logger.LogInformation("成功处理实例 {Count}: '{Name}' (类型: {Type})",
                        processedInstanceCount, instanceNameCleaned, instance.Type);

                    isFirstProcessedRow = false;
                }

                _logger.LogInformation("=== Excel读取完成 ===");
                _logger.LogInformation("总共处理了 {ProcessedRows} 行数据", maxRow - 1);
                _logger.LogInformation("成功读取 {Count} 个实例", instances.Count);
                _logger.LogInformation("OpMode注释: '{OpModeComment}'", opModeCommentForFooter);

                if (instances.Count == 0)
                {
                    _logger.LogWarning("未能从Excel文件中读取到有效数据");
                    _logger.LogWarning("请检查Excel文件是否包含有效的实例数据");
                    _logger.LogWarning("确保'实例名称'列不为空");
                }
                else
                {
                    _logger.LogInformation("实例列表:");
                    for (int i = 0; i < instances.Count; i++)
                    {
                        var inst = instances[i];
                        _logger.LogInformation("  {Index}. {Name} - {Comment} ({Type})",
                            i + 1, inst.Name, inst.Comment, inst.Type);
                    }
                }
            }
            catch (FileNotFoundException)
            {
                _logger.LogError("Excel file not found at path: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading Excel data: {Exception}", ex.Message);
            }

            return new ExcelReadResult
            {
                Instances = instances,
                UidParamMap = uidParamMap,
                OpModeComment = opModeCommentForFooter
            };
        }

        /// <summary>
        /// 获取单元格值作为字符串 - 对应Python代码中的get_cell_value_as_str函数
        /// </summary>
        private string GetCellValueAsString(object? cellValue)
        {
            if (cellValue == null)
                return string.Empty;
            return cellValue.ToString()?.Trim() ?? string.Empty;
        }

        /// <summary>
        /// 清理字符串引号 - 对应Python代码中的_clean_string_quotes函数
        /// </summary>
        private string CleanStringQuotes(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return string.Empty;

            return value.Trim('"');
        }


    }
}
