using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

// 全局设置EPPlus许可证（非商业用途）
[assembly: System.Runtime.CompilerServices.InternalsVisibleTo("EPPlus")]

namespace TiaXmlGenerator.Services
{
    /// <summary>
    /// Excel读取服务，对应Python中的read_excel_data方法
    /// </summary>
    public class ExcelReaderService : IExcelReaderService
    {
        private readonly ILogger<ExcelReaderService> _logger;



        public ExcelReaderService(ILogger<ExcelReaderService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 创建ExcelPackage实例，处理EPPlus 8.0许可证问题
        /// </summary>
        private ExcelPackage CreateExcelPackage(string filePath)
        {
            // 许可证应该已经在应用程序启动时设置，直接创建ExcelPackage
            return new ExcelPackage(new FileInfo(filePath));
        }

        /// <summary>
        /// 读取Excel数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>读取结果</returns>
        public async Task<ExcelReadResult> ReadExcelDataAsync(string filePath)
        {
            var result = new ExcelReadResult();

            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogError("Excel file not found at path: {FilePath}", filePath);
                    return result;
                }

                _logger.LogInformation("开始读取Excel文件: {FilePath}", filePath);

                using var package = CreateExcelPackage(filePath);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    _logger.LogError("No active sheet found in the Excel file.");
                    return result;
                }

                // 读取表头
                var headers = ReadHeaders(worksheet);
                _logger.LogInformation("Excel Header: {Headers}", string.Join(", ", headers));

                // 验证必需的列
                var missingColumns = ValidateRequiredColumns(headers);
                if (missingColumns.Any())
                {
                    _logger.LogError("Missing one or more required columns in Excel header: {MissingColumns}", string.Join(", ", missingColumns));
                    return result;
                }

                // 获取列索引
                var columnIndexes = GetColumnIndexes(headers);

                // 读取OpMode注释（用于footer）
                result.OpModeComment = ReadOpModeComment(worksheet, columnIndexes);

                // 读取实例数据
                result.Instances = await ReadInstancesAsync(worksheet, columnIndexes, headers);

                // 创建UID参数映射
                result.UidParamMap = CreateUidParamMap(result.Instances);

                _logger.LogInformation("Successfully read {Count} instances from Excel", result.Instances.Count);

                if (result.Instances.Count == 0)
                {
                    _logger.LogWarning("未能从Excel文件中读取到有效数据");
                }
            }
            catch (FileNotFoundException)
            {
                _logger.LogError("Excel file not found at path: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading Excel data: {Exception}", ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 读取表头
        /// </summary>
        private List<string> ReadHeaders(ExcelWorksheet worksheet)
        {
            var headers = new List<string>();
            var columnCount = worksheet.Dimension?.Columns ?? 0;

            for (int col = 1; col <= columnCount; col++)
            {
                var headerValue = worksheet.Cells[1, col].Value?.ToString()?.Trim() ?? string.Empty;
                headers.Add(headerValue);
            }

            return headers;
        }

        /// <summary>
        /// 验证必需的列
        /// </summary>
        private List<string> ValidateRequiredColumns(List<string> headers)
        {
            var requiredColumns = new[]
            {
                "实例名称", "实例注释",
                "I点-工作位1", "I点-原位1",
                "Q点-工作位", "Q点-原位"
            };

            return requiredColumns.Where(col => !headers.Contains(col)).ToList();
        }

        /// <summary>
        /// 获取列索引映射
        /// </summary>
        private Dictionary<string, int> GetColumnIndexes(List<string> headers)
        {
            var indexes = new Dictionary<string, int>();

            for (int i = 0; i < headers.Count; i++)
            {
                if (!string.IsNullOrWhiteSpace(headers[i]))
                {
                    indexes[headers[i]] = i + 1; // Excel列索引从1开始
                }
            }

            return indexes;
        }

        /// <summary>
        /// 读取OpMode注释
        /// </summary>
        private string ReadOpModeComment(ExcelWorksheet worksheet, Dictionary<string, int> columnIndexes)
        {
            const string opModeCommentColumn = "OpMode注释";

            if (columnIndexes.TryGetValue(opModeCommentColumn, out int colIndex) && worksheet.Dimension.Rows >= 2)
            {
                var comment = worksheet.Cells[2, colIndex].Value?.ToString()?.Trim() ?? string.Empty;
                _logger.LogInformation("Read OpMode comment from Excel: '{Comment}'", comment);
                return comment;
            }

            _logger.LogWarning("OpMode comment column not found or no data rows");
            return string.Empty;
        }

        /// <summary>
        /// 读取实例数据
        /// </summary>
        private async Task<List<InstanceData>> ReadInstancesAsync(ExcelWorksheet worksheet,
            Dictionary<string, int> columnIndexes, List<string> headers)
        {
            var instances = new List<InstanceData>();
            var rowCount = worksheet.Dimension?.Rows ?? 0;

            string firstDbName = string.Empty;
            string firstOpModeName = string.Empty;
            bool isFirstRow = true;

            for (int row = 2; row <= rowCount; row++) // 从第2行开始（跳过表头）
            {
                var instanceName = GetCellValue(worksheet, row, columnIndexes, "实例名称");

                // 跳过空行
                if (string.IsNullOrWhiteSpace(instanceName))
                    continue;

                var instance = new InstanceData
                {
                    Name = CleanStringQuotes(instanceName),
                    Comment = CleanStringQuotes(GetCellValue(worksheet, row, columnIndexes, "实例注释")),
                    ExcelRow = row
                };

                // 读取基本I/O配置
                ReadBasicIoConfiguration(worksheet, row, columnIndexes, instance);

                // 读取偏移量配置
                ReadOffsetConfiguration(worksheet, row, columnIndexes, instance);

                // 读取双输入配置
                ReadDoubleInputConfiguration(worksheet, row, columnIndexes, instance);

                // 读取其他配置
                ReadOtherConfiguration(worksheet, row, columnIndexes, instance,
                    ref firstDbName, ref firstOpModeName, ref isFirstRow);

                // 存储所有Excel数据
                StoreAllExcelData(worksheet, row, headers, instance);

                instances.Add(instance);
                _logger.LogDebug("Read instance: {Name} from row {Row}", instance.Name, row);
            }

            return instances;
        }

        /// <summary>
        /// 读取基本I/O配置
        /// </summary>
        private void ReadBasicIoConfiguration(ExcelWorksheet worksheet, int row,
            Dictionary<string, int> columnIndexes, InstanceData instance)
        {
            instance.ForwardSensorAddr = GetCellValue(worksheet, row, columnIndexes, "I点-工作位1");
            instance.BackwardSensorAddr = GetCellValue(worksheet, row, columnIndexes, "I点-原位1");
            instance.ForwardOutputAddr = GetCellValue(worksheet, row, columnIndexes, "Q点-工作位");
            instance.BackwardOutputAddr = GetCellValue(worksheet, row, columnIndexes, "Q点-原位");

            // 生成描述信息
            instance.ForwardSensorDesc = $"{instance.ForwardSensorAddr} {instance.Name}工作位";
            instance.BackwardSensorDesc = $"{instance.BackwardSensorAddr} {instance.Name}原位";
            instance.ForwardOutputDesc = $"{instance.ForwardOutputAddr} {instance.Name}工作位输出";
            instance.BackwardOutputDesc = $"{instance.BackwardOutputAddr} {instance.Name}原位输出";
        }

        /// <summary>
        /// 读取偏移量配置
        /// </summary>
        private void ReadOffsetConfiguration(ExcelWorksheet worksheet, int row,
            Dictionary<string, int> columnIndexes, InstanceData instance)
        {
            instance.ForwardSensorOffset = GetCellValue(worksheet, row, columnIndexes, "I点-工作位1偏移量");
            instance.BackwardSensorOffset = GetCellValue(worksheet, row, columnIndexes, "I点-原位1偏移量");
            instance.ForwardOutputOffset = GetCellValue(worksheet, row, columnIndexes, "Q点-工作位偏移量");
            instance.BackwardOutputOffset = GetCellValue(worksheet, row, columnIndexes, "Q点-原位偏移量");
        }

        /// <summary>
        /// 读取双输入配置
        /// </summary>
        private void ReadDoubleInputConfiguration(ExcelWorksheet worksheet, int row,
            Dictionary<string, int> columnIndexes, InstanceData instance)
        {
            var fwdSensor2 = GetCellValue(worksheet, row, columnIndexes, "I点-工作位2");
            var bwdSensor2 = GetCellValue(worksheet, row, columnIndexes, "I点-原位2");

            bool isDoubleInput = !string.IsNullOrWhiteSpace(fwdSensor2) || !string.IsNullOrWhiteSpace(bwdSensor2);

            if (isDoubleInput)
            {
                instance.IsDoubleInputType = true;
                instance.Type = "DoubleInput";
                instance.ForwardSensor2Addr = fwdSensor2;
                instance.BackwardSensor2Addr = bwdSensor2;
                instance.ForwardSensor2Desc = $"{fwdSensor2} {instance.Name}工作位2";
                instance.BackwardSensor2Desc = $"{bwdSensor2} {instance.Name}原位2";

                // 读取双输入偏移量
                instance.ForwardSensor2Offset = GetCellValue(worksheet, row, columnIndexes, "I点-工作位2偏移量");
                instance.BackwardSensor2Offset = GetCellValue(worksheet, row, columnIndexes, "I点-原位2偏移量");
            }
            else
            {
                instance.IsDoubleInputType = false;
                instance.Type = "SingleInput";
            }
        }

        /// <summary>
        /// 读取其他配置
        /// </summary>
        private void ReadOtherConfiguration(ExcelWorksheet worksheet, int row,
            Dictionary<string, int> columnIndexes, InstanceData instance,
            ref string firstDbName, ref string firstOpModeName, ref bool isFirstRow)
        {
            // 读取DB名称
            var currentDbName = GetCellValue(worksheet, row, columnIndexes, "手动行DB块名称");
            if (isFirstRow && !string.IsNullOrWhiteSpace(currentDbName))
                firstDbName = currentDbName;
            instance.ManualDbName = !string.IsNullOrWhiteSpace(currentDbName) ? currentDbName : firstDbName;

            // 读取OpMode名称
            var currentOpModeName = GetCellValue(worksheet, row, columnIndexes, "OpMode名称");
            if (isFirstRow && !string.IsNullOrWhiteSpace(currentOpModeName))
                firstOpModeName = currentOpModeName;
            instance.OpModeName = !string.IsNullOrWhiteSpace(currentOpModeName) ? currentOpModeName : firstOpModeName;

            // 读取其他字段
            instance.OpModeNumber = GetCellValue(worksheet, row, columnIndexes, "OpMode编号");
            instance.FbTemplate = GetCellValue(worksheet, row, columnIndexes, "手动FB 块编号");

            isFirstRow = false;
        }

        /// <summary>
        /// 存储所有Excel数据
        /// </summary>
        private void StoreAllExcelData(ExcelWorksheet worksheet, int row, List<string> headers, InstanceData instance)
        {
            for (int col = 0; col < headers.Count; col++)
            {
                var header = headers[col];
                if (!string.IsNullOrWhiteSpace(header))
                {
                    var value = worksheet.Cells[row, col + 1].Value?.ToString()?.Trim() ?? string.Empty;
                    instance.SetExcelValue(header, value);
                }
            }
        }

        /// <summary>
        /// 获取单元格值
        /// </summary>
        private string GetCellValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> columnIndexes, string columnName)
        {
            if (columnIndexes.TryGetValue(columnName, out int colIndex))
            {
                return worksheet.Cells[row, colIndex].Value?.ToString()?.Trim() ?? string.Empty;
            }
            return string.Empty;
        }

        /// <summary>
        /// 清理字符串引号
        /// </summary>
        private string CleanStringQuotes(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return string.Empty;

            return value.Trim('"');
        }

        /// <summary>
        /// 创建UID参数映射
        /// </summary>
        private Dictionary<string, object> CreateUidParamMap(List<InstanceData> instances)
        {
            var uidParamMap = new Dictionary<string, object>();

            foreach (var instance in instances)
            {
                uidParamMap[instance.Name] = instance;
            }

            return uidParamMap;
        }
    }
}
