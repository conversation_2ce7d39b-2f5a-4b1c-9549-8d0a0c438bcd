using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

// 全局设置EPPlus许可证（非商业用途）
[assembly: System.Runtime.CompilerServices.InternalsVisibleTo("EPPlus")]

namespace TiaXmlGenerator.Services
{
    /// <summary>
    /// Excel读取服务，对应Python中的read_excel_data方法
    /// </summary>
    public class ExcelReaderService : IExcelReaderService
    {
        private readonly ILogger<ExcelReaderService> _logger;



        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public ExcelReaderService(ILogger<ExcelReaderService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 创建ExcelPackage实例，处理EPPlus 8.0许可证问题
        /// </summary>
        private ExcelPackage CreateExcelPackage(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                _logger.LogInformation("Excel文件信息: 路径={Path}, 大小={Size}字节, 存在={Exists}",
                    filePath, fileInfo.Length, fileInfo.Exists);

                // 许可证应该已经在应用程序启动时设置，直接创建ExcelPackage
                var package = new ExcelPackage(fileInfo);
                _logger.LogInformation("Excel包创建成功，工作表数量: {WorksheetCount}", package.Workbook.Worksheets.Count);
                return package;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建Excel包时发生错误: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 读取Excel数据 - 严格按照Python代码的read_excel_data方法实现
        /// Python: def read_excel_data(self, excel_file_path: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>读取结果</returns>
        public Task<ExcelReadResult> ReadExcelDataAsync(string filePath)
        {
            // Python: instances = []
            var instances = new List<InstanceData>();
            // Python: uid_param_map = {}  # Stores UID to parameter mapping
            var uidParamMap = new Dictionary<string, object>();
            // Python: op_mode_comment_for_footer = "" # Initialize to empty string
            var opModeCommentForFooter = string.Empty;

            try
            {
                // Python: workbook = openpyxl.load_workbook(excel_file_path, data_only=True)
                _logger.LogInformation("开始读取Excel文件: {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogError("Excel文件不存在: {FilePath}", filePath);
                    return Task.FromResult(new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter });
                }

                _logger.LogInformation("正在创建Excel包...");
                using var package = CreateExcelPackage(filePath);
                _logger.LogInformation("Excel包创建成功");

                // Python: sheet = workbook.active
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                // Python: if sheet is None:
                if (worksheet == null)
                {
                    // Python: self.logger.error("No active sheet found in the Excel file.")
                    _logger.LogError("No active sheet found in the Excel file.");
                    // Python: return [], {}
                    return Task.FromResult(new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter });
                }

                // Python: header = [get_cell_value_as_str(sheet.cell(row=1, column=i).value) for i in range(1, sheet.max_column + 1)]
                var maxColumn = worksheet.Dimension?.Columns ?? 0;
                var header = new List<string>();
                for (int i = 1; i <= maxColumn; i++)
                {
                    var cellValue = worksheet.Cells[1, i].Value;
                    header.Add(GetCellValueAsString(cellValue));
                }

                // Python: self.logger.info(f"Excel Header: {header}")
                _logger.LogInformation("Excel Header: [{Headers}]", string.Join(", ", header));

                // Python: # 检查Excel表头中是否存在预期的列
                // Python: expected_columns = ['实例名称', '实例注释', 'I点-工作位1', 'I点-原位1', 'Q点-工作位', 'Q点-原位']
                var expectedColumns = new[]
                {
                    "实例名称", "实例注释",
                    "I点-工作位1", "I点-原位1",
                    "Q点-工作位", "Q点-原位"
                };

                // Python: # 验证所需列是否存在
                // Python: missing_columns = []
                var missingColumns = new List<string>();

                // Python: for col in expected_columns:
                //     if col not in header:
                //         missing_columns.append(col)
                foreach (var col in expectedColumns)
                {
                    if (!header.Contains(col))
                    {
                        missingColumns.Add(col);
                    }
                }

                // Python: if missing_columns:
                //     self.logger.error(f"Missing one or more required columns in Excel header: {', '.join(missing_columns)}")
                //     return [], {}
                if (missingColumns.Any())
                {
                    _logger.LogError("Missing one or more required columns in Excel header: {MissingColumns}", string.Join(", ", missingColumns));
                    return Task.FromResult(new ExcelReadResult { Instances = instances, UidParamMap = uidParamMap, OpModeComment = opModeCommentForFooter });
                }

                // Python: # 获取列索引
                // Python: name_col = header.index("实例名称") + 1
                var nameCol = header.IndexOf("实例名称") + 1;
                // Python: comment_col = header.index("实例注释") + 1
                var commentCol = header.IndexOf("实例注释") + 1;

                // Python: # I/O 地址列
                // Python: fwd_sensor_addr_col = header.index("I点-工作位1") + 1
                var fwdSensorAddrCol = header.IndexOf("I点-工作位1") + 1;
                // Python: bwd_sensor_addr_col = header.index("I点-原位1") + 1
                var bwdSensorAddrCol = header.IndexOf("I点-原位1") + 1;
                // Python: fwd_output_addr_col = header.index("Q点-工作位") + 1
                var fwdOutputAddrCol = header.IndexOf("Q点-工作位") + 1;
                // Python: bwd_output_addr_col = header.index("Q点-原位") + 1
                var bwdOutputAddrCol = header.IndexOf("Q点-原位") + 1;

                // Python: # 可选列偏移量
                // Python: fwd_sensor_offset_col = header.index("I点-工作位1偏移量") + 1 if "I点-工作位1偏移量" in header else -1
                var fwdSensorOffsetCol = header.Contains("I点-工作位1偏移量") ? header.IndexOf("I点-工作位1偏移量") + 1 : -1;
                // Python: bwd_sensor_offset_col = header.index("I点-原位1偏移量") + 1 if "I点-原位1偏移量" in header else -1
                var bwdSensorOffsetCol = header.Contains("I点-原位1偏移量") ? header.IndexOf("I点-原位1偏移量") + 1 : -1;
                // Python: fwd_output_offset_col = header.index("Q点-工作位偏移量") + 1 if "Q点-工作位偏移量" in header else -1
                var fwdOutputOffsetCol = header.Contains("Q点-工作位偏移量") ? header.IndexOf("Q点-工作位偏移量") + 1 : -1;
                // Python: bwd_output_offset_col = header.index("Q点-原位偏移量") + 1 if "Q点-原位偏移量" in header else -1
                var bwdOutputOffsetCol = header.Contains("Q点-原位偏移量") ? header.IndexOf("Q点-原位偏移量") + 1 : -1;

                // Python: # 双输入点的可选列
                // Python: fwd_sensor2_col = header.index("I点-工作位2") + 1 if "I点-工作位2" in header else -1
                var fwdSensor2Col = header.Contains("I点-工作位2") ? header.IndexOf("I点-工作位2") + 1 : -1;
                // Python: bwd_sensor2_col = header.index("I点-原位2") + 1 if "I点-原位2" in header else -1
                var bwdSensor2Col = header.Contains("I点-原位2") ? header.IndexOf("I点-原位2") + 1 : -1;
                // Python: fwd_sensor2_offset_col = header.index("I点-工作位2偏移量") + 1 if "I点-工作位2偏移量" in header else -1
                var fwdSensor2OffsetCol = header.Contains("I点-工作位2偏移量") ? header.IndexOf("I点-工作位2偏移量") + 1 : -1;
                // Python: bwd_sensor2_offset_col = header.index("I点-原位2偏移量") + 1 if "I点-原位2偏移量" in header else -1
                var bwdSensor2OffsetCol = header.Contains("I点-原位2偏移量") ? header.IndexOf("I点-原位2偏移量") + 1 : -1;

                // Python: # 其他可能的列
                // Python: db_name_col = header.index("手动行DB块名称") + 1 if "手动行DB块名称" in header else -1
                var dbNameCol = header.Contains("手动行DB块名称") ? header.IndexOf("手动行DB块名称") + 1 : -1;
                // Python: op_mode_name_col = header.index("OpMode名称") + 1 if "OpMode名称" in header else -1
                var opModeNameCol = header.Contains("OpMode名称") ? header.IndexOf("OpMode名称") + 1 : -1;
                // Python: op_mode_number_col = header.index("OpMode编号") + 1 if "OpMode编号" in header else -1
                var opModeNumberCol = header.Contains("OpMode编号") ? header.IndexOf("OpMode编号") + 1 : -1;
                // Python: module_name_col_c_header = "OpMode注释" # CORRECTED based on user screenshot
                var moduleNameColCHeader = "OpMode注释";
                // Python: module_name_col_c = header.index(module_name_col_c_header) + 1 if module_name_col_c_header in header else -1
                var moduleNameColC = header.Contains(moduleNameColCHeader) ? header.IndexOf(moduleNameColCHeader) + 1 : -1;

                // Python: # FB模板编号
                // Python: fb_template_col = header.index("手动FB 块编号") + 1 if "手动FB 块编号" in header else -1
                var fbTemplateCol = header.Contains("手动FB 块编号") ? header.IndexOf("手动FB 块编号") + 1 : -1;

                // Python: # Read the OpMode注释 from the first data row (e.g., row 2) for the footer
                // Python: if module_name_col_c != -1 and sheet.max_row >= 2:
                var maxRow = worksheet.Dimension?.Rows ?? 0;
                if (moduleNameColC != -1 && maxRow >= 2)
                {
                    // Python: op_mode_comment_for_footer = get_cell_value_as_str(sheet.cell(row=2, column=module_name_col_c).value)
                    opModeCommentForFooter = GetCellValueAsString(worksheet.Cells[2, moduleNameColC].Value);
                    // Python: self.logger.info(f"Read '{module_name_col_c_header}' for footer from Excel row 2: '{op_mode_comment_for_footer}'")
                    _logger.LogInformation("Read '{Header}' for footer from Excel row 2: '{Comment}'", moduleNameColCHeader, opModeCommentForFooter);
                }
                // Python: elif module_name_col_c == -1:
                else if (moduleNameColC == -1)
                {
                    // Python: self.logger.warning(f"Excel column '{module_name_col_c_header}' (expected for Column C) not found. Footer text replacement will use an empty string.")
                    _logger.LogWarning("Excel column '{Header}' (expected for Column C) not found. Footer text replacement will use an empty string.", moduleNameColCHeader);
                }
                // Python: else: # Column exists but sheet has less than 2 rows
                else
                {
                    // Python: self.logger.warning(f"Excel column '{module_name_col_c_header}' found, but sheet has less than 2 rows. Cannot read footer comment.")
                    _logger.LogWarning("Excel column '{Header}' found, but sheet has less than 2 rows. Cannot read footer comment.", moduleNameColCHeader);
                }

                // Python: first_processed_row_db_name = ""
                var firstProcessedRowDbName = string.Empty;
                // Python: first_processed_row_op_mode_name = ""
                var firstProcessedRowOpModeName = string.Empty;
                // Python: is_first_processed_row = True
                var isFirstProcessedRow = true;

                // Python: for current_row_idx in range(2, sheet.max_row + 1):
                var processedInstanceCount = 0;
                for (int currentRowIdx = 2; currentRowIdx <= maxRow; currentRowIdx++)
                {
                    // Python: instance_name = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=name_col).value)
                    var instanceName = GetCellValueAsString(worksheet.Cells[currentRowIdx, nameCol].Value);
                    // Python: instance_comment = get_cell_value_as_str(sheet.cell(row=current_row_idx, column=comment_col).value)
                    var instanceComment = GetCellValueAsString(worksheet.Cells[currentRowIdx, commentCol].Value);

                    // Python: # 如果实例名称为空则跳过
                    // Python: if not instance_name.strip():
                    //     continue
                    if (string.IsNullOrWhiteSpace(instanceName))
                        continue;

                    // 清洁字符串值
                    var instanceNameCleaned = CleanStringQuotes(instanceName);
                    var instanceCommentCleaned = CleanStringQuotes(instanceComment);

                    // 读取基本地址数据
                    var fwdSensorAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensorAddrCol].Value);
                    var bwdSensorAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensorAddrCol].Value);
                    var fwdOutputAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdOutputAddrCol].Value);
                    var bwdOutputAddr = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdOutputAddrCol].Value);

                    // 创建实例数据对象
                    var instance = new InstanceData
                    {
                        Name = instanceNameCleaned,
                        Comment = instanceCommentCleaned,
                        ExcelRow = currentRowIdx,
                        ForwardSensorAddr = fwdSensorAddr,
                        BackwardSensorAddr = bwdSensorAddr,
                        ForwardOutputAddr = fwdOutputAddr,
                        BackwardOutputAddr = bwdOutputAddr,
                        ForwardSensorDesc = $"{fwdSensorAddr} {instanceNameCleaned}工作位",
                        BackwardSensorDesc = $"{bwdSensorAddr} {instanceNameCleaned}原位",
                        ForwardOutputDesc = $"{fwdOutputAddr} {instanceNameCleaned}工作位输出",
                        BackwardOutputDesc = $"{bwdOutputAddr} {instanceNameCleaned}原位输出"
                    };

                    // 存储Excel原始列数据
                    instance.SetExcelValue("I点-工作位1", fwdSensorAddr);
                    instance.SetExcelValue("I点-原位1", bwdSensorAddr);
                    instance.SetExcelValue("Q点-工作位", fwdOutputAddr);
                    instance.SetExcelValue("Q点-原位", bwdOutputAddr);

                    // 添加偏移量数据（如果存在）
                    if (fwdSensorOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensorOffsetCol].Value);
                        instance.SetExcelValue("I点-工作位1偏移量", value);
                        instance.ForwardSensorOffset = value;
                    }
                    if (bwdSensorOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensorOffsetCol].Value);
                        instance.SetExcelValue("I点-原位1偏移量", value);
                        instance.BackwardSensorOffset = value;
                    }
                    if (fwdOutputOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdOutputOffsetCol].Value);
                        instance.SetExcelValue("Q点-工作位偏移量", value);
                        instance.ForwardOutputOffset = value;
                    }
                    if (bwdOutputOffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdOutputOffsetCol].Value);
                        instance.SetExcelValue("Q点-原位偏移量", value);
                        instance.BackwardOutputOffset = value;
                    }

                    // 添加双输入点数据（如果存在）
                    var isDoubleInput = false;
                    if (fwdSensor2Col != -1)
                    {
                        var fwdSensor2 = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensor2Col].Value);
                        if (!string.IsNullOrWhiteSpace(fwdSensor2))
                        {
                            instance.SetExcelValue("I点-工作位2", fwdSensor2);
                            instance.ForwardSensor2Addr = fwdSensor2;
                            instance.ForwardSensor2Desc = $"{fwdSensor2} {instanceNameCleaned}工作位2";
                            isDoubleInput = true;
                        }
                    }

                    if (bwdSensor2Col != -1)
                    {
                        var bwdSensor2 = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensor2Col].Value);
                        if (!string.IsNullOrWhiteSpace(bwdSensor2))
                        {
                            instance.SetExcelValue("I点-原位2", bwdSensor2);
                            instance.BackwardSensor2Addr = bwdSensor2;
                            instance.BackwardSensor2Desc = $"{bwdSensor2} {instanceNameCleaned}原位2";
                            isDoubleInput = true;
                        }
                    }

                    // 添加双输入点偏移量（如果存在）
                    if (fwdSensor2OffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, fwdSensor2OffsetCol].Value);
                        instance.SetExcelValue("I点-工作位2偏移量", value);
                        instance.ForwardSensor2Offset = value;
                    }
                    if (bwdSensor2OffsetCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, bwdSensor2OffsetCol].Value);
                        instance.SetExcelValue("I点-原位2偏移量", value);
                        instance.BackwardSensor2Offset = value;
                    }

                    // 设置实例类型
                    instance.IsDoubleInputType = isDoubleInput;
                    instance.Type = isDoubleInput ? "DoubleInput" : "SingleInput";

                    // 添加其他可能的数据
                    if (dbNameCol != -1)
                    {
                        var currentDbVal = GetCellValueAsString(worksheet.Cells[currentRowIdx, dbNameCol].Value);
                        if (isFirstProcessedRow && !string.IsNullOrWhiteSpace(currentDbVal))
                            firstProcessedRowDbName = currentDbVal;
                        var dbName = !string.IsNullOrWhiteSpace(currentDbVal) ? currentDbVal : firstProcessedRowDbName;
                        instance.SetExcelValue("手动行DB块名称", dbName);
                        instance.ManualDbName = dbName;
                    }

                    if (opModeNameCol != -1)
                    {
                        var currentOpModeVal = GetCellValueAsString(worksheet.Cells[currentRowIdx, opModeNameCol].Value);
                        if (isFirstProcessedRow && !string.IsNullOrWhiteSpace(currentOpModeVal))
                            firstProcessedRowOpModeName = currentOpModeVal;
                        var opModeName = !string.IsNullOrWhiteSpace(currentOpModeVal) ? currentOpModeVal : firstProcessedRowOpModeName;
                        instance.SetExcelValue("OpMode名称", opModeName);
                        instance.OpModeName = opModeName;
                    }

                    if (opModeNumberCol != -1)
                    {
                        var value = GetCellValueAsString(worksheet.Cells[currentRowIdx, opModeNumberCol].Value);
                        instance.SetExcelValue("OpMode编号", value);
                        instance.OpModeNumber = value;
                    }

                    // 获取FB模板编号（如果存在）
                    if (fbTemplateCol != -1)
                    {
                        var fbTemplateName = GetCellValueAsString(worksheet.Cells[currentRowIdx, fbTemplateCol].Value);
                        instance.FbTemplate = fbTemplateName;
                    }

                    instances.Add(instance);
                    processedInstanceCount++;

                    // 使用实例名称作为 uid_param_map 的键
                    uidParamMap[instanceNameCleaned] = instance;

                    _logger.LogInformation("成功处理实例 {Count}: '{Name}' (类型: {Type})",
                        processedInstanceCount, instanceNameCleaned, instance.Type);

                    isFirstProcessedRow = false;
                }

                _logger.LogInformation("=== Excel读取完成 ===");
                _logger.LogInformation("总共处理了 {ProcessedRows} 行数据", maxRow - 1);
                _logger.LogInformation("成功读取 {Count} 个实例", instances.Count);
                _logger.LogInformation("OpMode注释: '{OpModeComment}'", opModeCommentForFooter);

                if (instances.Count == 0)
                {
                    _logger.LogWarning("未能从Excel文件中读取到有效数据");
                    _logger.LogWarning("请检查Excel文件是否包含有效的实例数据");
                    _logger.LogWarning("确保'实例名称'列不为空");
                }
                else
                {
                    _logger.LogInformation("实例列表:");
                    for (int i = 0; i < instances.Count; i++)
                    {
                        var inst = instances[i];
                        _logger.LogInformation("  {Index}. {Name} - {Comment} ({Type})",
                            i + 1, inst.Name, inst.Comment, inst.Type);
                    }
                }
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, "Excel file not found at path: {FilePath}", filePath);
                _logger.LogError("FileNotFoundException详细信息: {Message}", ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading Excel data: {Exception}", ex.Message);
                _logger.LogError("异常类型: {ExceptionType}", ex.GetType().Name);
                _logger.LogError("异常堆栈: {StackTrace}", ex.StackTrace);
                if (ex.InnerException != null)
                {
                    _logger.LogError("内部异常: {InnerException}", ex.InnerException.Message);
                }
            }

            return Task.FromResult(new ExcelReadResult
            {
                Instances = instances,
                UidParamMap = uidParamMap,
                OpModeComment = opModeCommentForFooter
            });
        }

        /// <summary>
        /// 获取单元格值作为字符串 - 对应Python代码中的get_cell_value_as_str函数
        /// </summary>
        private string GetCellValueAsString(object? cellValue)
        {
            if (cellValue == null)
                return string.Empty;
            return cellValue.ToString()?.Trim() ?? string.Empty;
        }

        /// <summary>
        /// 清理字符串引号 - 对应Python代码中的_clean_string_quotes函数
        /// </summary>
        private string CleanStringQuotes(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return string.Empty;

            return value.Trim('"');
        }


    }
}













