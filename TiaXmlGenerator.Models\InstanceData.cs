using System.Collections.Generic;

namespace TiaXmlGenerator.Models
{
    /// <summary>
    /// 实例数据模型，对应Python中的instance_data字典
    /// </summary>
    public class InstanceData
    {
        public string Name { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public int ExcelRow { get; set; }
        public bool IsDoubleInputType { get; set; }
        public string Type { get; set; } = string.Empty;

        // I/O 地址配置
        public string ForwardSensorAddr { get; set; } = string.Empty;
        public string BackwardSensorAddr { get; set; } = string.Empty;
        public string ForwardOutputAddr { get; set; } = string.Empty;
        public string BackwardOutputAddr { get; set; } = string.Empty;

        // 描述信息
        public string ForwardSensorDesc { get; set; } = string.Empty;
        public string BackwardSensorDesc { get; set; } = string.Empty;
        public string ForwardOutputDesc { get; set; } = string.Empty;
        public string BackwardOutputDesc { get; set; } = string.Empty;

        // 双输入类型的额外配置
        public string ForwardSensor2Addr { get; set; } = string.Empty;
        public string BackwardSensor2Addr { get; set; } = string.Empty;
        public string ForwardSensor2Desc { get; set; } = string.Empty;
        public string BackwardSensor2Desc { get; set; } = string.Empty;

        // 偏移量配置
        public string ForwardSensorOffset { get; set; } = string.Empty;
        public string BackwardSensorOffset { get; set; } = string.Empty;
        public string ForwardOutputOffset { get; set; } = string.Empty;
        public string BackwardOutputOffset { get; set; } = string.Empty;
        public string ForwardSensor2Offset { get; set; } = string.Empty;
        public string BackwardSensor2Offset { get; set; } = string.Empty;

        // 其他配置
        public string ManualDbName { get; set; } = string.Empty;
        public string OpModeName { get; set; } = string.Empty;
        public string OpModeNumber { get; set; } = string.Empty;
        public string FbTemplate { get; set; } = string.Empty;

        // Excel列数据的原始存储（用于参数映射）
        public Dictionary<string, string> ExcelData { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 获取Excel列数据
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>列值</returns>
        public string GetExcelValue(string columnName)
        {
            return ExcelData.TryGetValue(columnName, out var value) ? value : string.Empty;
        }

        /// <summary>
        /// 设置Excel列数据
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="value">列值</param>
        public void SetExcelValue(string columnName, string value)
        {
            ExcelData[columnName] = value ?? string.Empty;
        }
    }

    /// <summary>
    /// OpMode数据模型
    /// </summary>
    public class OpModeData
    {
        public string FbName { get; set; } = string.Empty;
        public string OpModeName { get; set; } = string.Empty;
        public string OpModeNumber { get; set; } = string.Empty;
        public string OpModeComment { get; set; } = string.Empty;
        public int FbNumber { get; set; }
    }

    /// <summary>
    /// 生成配置
    /// </summary>
    public class GenerationConfig
    {
        public string ExcelFilePath { get; set; } = string.Empty;
        public string TemplateFilePath { get; set; } = string.Empty;
        public string OutputFilePath { get; set; } = string.Empty;
        public int FbInstanceSize { get; set; } = 256;
    }

    /// <summary>
    /// 参数映射配置
    /// </summary>
    public class ParameterMapping
    {
        public string Section { get; set; } = string.Empty;
        public string Member { get; set; } = string.Empty;
        public bool IsBool { get; set; }
    }

    /// <summary>
    /// Parts修改规则
    /// </summary>
    public class PartsModificationRule
    {
        public List<ComponentRule> Components { get; set; } = new List<ComponentRule>();
        public List<AddressRule> Addresses { get; set; } = new List<AddressRule>();
        public List<ConstantRule> Constants { get; set; } = new List<ConstantRule>();
    }

    /// <summary>
    /// 组件规则
    /// </summary>
    public class ComponentRule
    {
        public string OriginalName { get; set; } = string.Empty;
        public string NewNameExcelCol { get; set; } = string.Empty;
    }

    /// <summary>
    /// 地址规则
    /// </summary>
    public class AddressRule
    {
        public string NewBitOffsetExcelCol { get; set; } = string.Empty;
    }

    /// <summary>
    /// 常量规则
    /// </summary>
    public class ConstantRule
    {
        public string OriginalValue { get; set; } = string.Empty;
        public string NewValueExcelCol { get; set; } = string.Empty;
    }

    /// <summary>
    /// Excel读取结果
    /// </summary>
    public class ExcelReadResult
    {
        public List<InstanceData> Instances { get; set; } = new List<InstanceData>();
        public Dictionary<string, object> UidParamMap { get; set; } = new Dictionary<string, object>();
        public string OpModeComment { get; set; } = string.Empty;
    }
}
