&lt;Window x:Class="TiaXmlGenerator.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="TIA Portal XML生成器" 
        Height="600" Width="800"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterScreen"
        Background="#F0F2F5">

    <Window.Resources>
        <!-- 主题颜色 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#1890FF"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#52C41A"/>
        <SolidColorBrush x:Key="ErrorBrush" Color="#F5222D"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="FrameBrush" Color="#FFFFFF"/>
        
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#40A9FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#096DD9"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#D9D9D9"/>
                                <Setter Property="Foreground" Value="#FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#73D13D"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#389E0D"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 文本框样式 -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- GroupBox样式 -->
        <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Background" Value="{StaticResource FrameBrush}"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" 
                                    BorderBrush="{TemplateBinding BorderBrush}" 
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <ContentPresenter Grid.Row="0" 
                                                    ContentSource="Header" 
                                                    Margin="10,8,10,0"/>
                                    <ContentPresenter Grid.Row="1" 
                                                    Margin="{TemplateBinding Padding}"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Background="{StaticResource BackgroundBrush}" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" 
                       Text="TIA Portal XML生成器" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       FontFamily="Microsoft YaHei UI"
                       Foreground="{StaticResource PrimaryBrush}" 
                       VerticalAlignment="Center"/>
            
            <TextBlock Grid.Column="1" 
                       Text="{Binding CurrentTime}" 
                       FontSize="12" 
                       FontFamily="Microsoft YaHei UI"
                       Foreground="#888888" 
                       VerticalAlignment="Center"/>
        </Grid>

        <!-- 文件设置区域 -->
        <GroupBox Grid.Row="1" Header="文件设置" Style="{StaticResource ModernGroupBoxStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Excel文件 -->
                <Grid Grid.Row="0" Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="Excel文件:" VerticalAlignment="Center" FontWeight="Normal"/>
                    <TextBox Grid.Column="1" 
                             Text="{Binding ExcelFilePath}" 
                             Style="{StaticResource ModernTextBoxStyle}" 
                             Margin="5,0"/>
                    <Button Grid.Column="2" 
                            Content="浏览..." 
                            Command="{Binding BrowseExcelCommand}"
                            Style="{StaticResource PrimaryButtonStyle}" 
                            Width="80" 
                            Margin="5,0,0,0"/>
                </Grid>

                <!-- 模板XML -->
                <Grid Grid.Row="1" Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="模板XML:" VerticalAlignment="Center" FontWeight="Normal"/>
                    <TextBox Grid.Column="1" 
                             Text="{Binding TemplateFilePath}" 
                             Style="{StaticResource ModernTextBoxStyle}" 
                             Margin="5,0"/>
                    <Button Grid.Column="2" 
                            Content="浏览..." 
                            Command="{Binding BrowseTemplateCommand}"
                            Style="{StaticResource PrimaryButtonStyle}" 
                            Width="80" 
                            Margin="5,0,0,0"/>
                </Grid>

                <!-- 输出XML -->
                <Grid Grid.Row="2" Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="输出XML:" VerticalAlignment="Center" FontWeight="Normal"/>
                    <TextBox Grid.Column="1" 
                             Text="{Binding OutputFilePath}" 
                             Style="{StaticResource ModernTextBoxStyle}" 
                             Margin="5,0"/>
                    <Button Grid.Column="2" 
                            Content="浏览..." 
                            Command="{Binding BrowseOutputCommand}"
                            Style="{StaticResource PrimaryButtonStyle}" 
                            Width="80" 
                            Margin="5,0,0,0"/>
                </Grid>
            </Grid>
        </GroupBox>

        <!-- 操作区域 -->
        <Grid Grid.Row="2" Margin="20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" 
                       Text="{Binding StatusMessage}" 
                       VerticalAlignment="Center" 
                       FontSize="12" 
                       FontFamily="Microsoft YaHei UI"
                       Foreground="#888888"/>
            
            <Button Grid.Column="1" 
                    Content="清空日志" 
                    Command="{Binding ClearLogCommand}"
                    Style="{StaticResource PrimaryButtonStyle}" 
                    Width="80" 
                    Margin="0,0,10,0"/>
            
            <Button Grid.Column="2" 
                    Content="生成XML文件" 
                    Command="{Binding GenerateXmlCommand}"
                    Style="{StaticResource SecondaryButtonStyle}" 
                    Width="120" 
                    FontSize="14" 
                    FontWeight="Bold"/>
        </Grid>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="3" 
                     Value="{Binding ProgressValue}" 
                     Height="6" 
                     Margin="20,0,20,10" 
                     VerticalAlignment="Top"
                     Background="#F0F0F0" 
                     Foreground="{StaticResource PrimaryBrush}"/>

        <!-- 日志显示区域 -->
        <GroupBox Grid.Row="3" Header="操作日志" Style="{StaticResource ModernGroupBoxStyle}" Margin="10,20,10,10">
            <Grid>
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <ListBox ItemsSource="{Binding LogMessages}" 
                             Background="#FAFAFA" 
                             BorderThickness="0"
                             FontFamily="Consolas" 
                             FontSize="11"
                             Foreground="#333333">
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="5,2"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ListBoxItem">
                                            <ContentPresenter Margin="{TemplateBinding Padding}"/>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </ScrollViewer>
            </Grid>
        </GroupBox>

        <!-- 状态栏 -->
        <Border Grid.Row="4" Background="#E6F7FF" BorderBrush="#D9D9D9" BorderThickness="0,1,0,0">
            <TextBlock Text="TIA Portal XML生成器 v1.0 | Made with ♥" 
                       FontSize="10" 
                       FontFamily="Microsoft YaHei UI"
                       Foreground="#888888" 
                       Margin="10,4" 
                       VerticalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
