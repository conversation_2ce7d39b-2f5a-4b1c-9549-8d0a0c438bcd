<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <AssemblyTitle>TIA XML Generator</AssemblyTitle>
    <AssemblyDescription>TIA Portal XML Generator Desktop Application</AssemblyDescription>
    <AssemblyCompany>TIA XML Generator</AssemblyCompany>
    <AssemblyProduct>TIA XML Generator</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TiaXmlGenerator.Core\TiaXmlGenerator.Core.csproj" />
    <ProjectReference Include="..\TiaXmlGenerator.Models\TiaXmlGenerator.Models.csproj" />
    <ProjectReference Include="..\TiaXmlGenerator.Services\TiaXmlGenerator.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**" />
  </ItemGroup>



</Project>
