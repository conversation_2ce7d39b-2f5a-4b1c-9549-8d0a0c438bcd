<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TiaXmlGenerator.Services</name>
    </assembly>
    <members>
        <member name="T:TiaXmlGenerator.Services.ExcelReaderService">
            <summary>
            Excel读取服务，对应Python中的read_excel_data方法
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.ExcelReaderService.#ctor(Microsoft.Extensions.Logging.ILogger{TiaXmlGenerator.Services.ExcelReaderService})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:TiaXmlGenerator.Services.ExcelReaderService.CreateExcelPackage(System.String)">
            <summary>
            创建ExcelPackage实例，处理EPPlus 8.0许可证问题
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.ExcelReaderService.ReadExcelDataAsync(System.String)">
            <summary>
            读取Excel数据 - 严格按照Python代码的read_excel_data方法实现
            Python: def read_excel_data(self, excel_file_path: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
            </summary>
            <param name="filePath">Excel文件路径</param>
            <returns>读取结果</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.ExcelReaderService.GetCellValueAsString(System.Object)">
            <summary>
            获取单元格值作为字符串 - 对应Python代码中的get_cell_value_as_str函数
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.ExcelReaderService.CleanStringQuotes(System.String)">
            <summary>
            清理字符串引号 - 对应Python代码中的_clean_string_quotes函数
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.IXmlGeneratorService">
            <summary>
            XML生成服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IXmlGeneratorService.GenerateXmlAsync(TiaXmlGenerator.Models.GenerationConfig)">
            <summary>
            生成XML文件
            </summary>
            <param name="config">生成配置</param>
            <returns>是否成功</returns>
        </member>
        <member name="E:TiaXmlGenerator.Services.Interfaces.IXmlGeneratorService.LogMessage">
            <summary>
            日志消息事件
            </summary>
        </member>
        <member name="E:TiaXmlGenerator.Services.Interfaces.IXmlGeneratorService.ProgressChanged">
            <summary>
            进度变化事件
            </summary>
        </member>
        <member name="E:TiaXmlGenerator.Services.Interfaces.IXmlGeneratorService.StatusChanged">
            <summary>
            状态变化事件
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.IExcelReaderService">
            <summary>
            Excel读取服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IExcelReaderService.ReadExcelDataAsync(System.String)">
            <summary>
            读取Excel数据
            </summary>
            <param name="filePath">Excel文件路径</param>
            <returns>读取结果</returns>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.INetworkGeneratorService">
            <summary>
            网络生成服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.INetworkGeneratorService.GenerateAllNetworks(System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData},TiaXmlGenerator.Models.OpModeData)">
            <summary>
            生成所有网络代码
            </summary>
            <param name="instances">实例列表</param>
            <param name="opModeData">OpMode数据</param>
            <returns>网络代码行列表</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.INetworkGeneratorService.GenerateFixedNetworksPart">
            <summary>
            生成固定网络部分
            </summary>
            <returns>固定网络代码行列表</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.INetworkGeneratorService.GenerateInstanceNetwork(TiaXmlGenerator.Models.InstanceData,TiaXmlGenerator.Models.OpModeData,System.Boolean)">
            <summary>
            生成实例网络
            </summary>
            <param name="instanceData">实例数据</param>
            <param name="opModeData">OpMode数据</param>
            <param name="isLastInstance">是否为最后一个实例</param>
            <returns>实例网络代码行列表</returns>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.IIdGeneratorService">
            <summary>
            ID生成服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IIdGeneratorService.GetNextId">
            <summary>
            获取下一个ID
            </summary>
            <returns>十六进制ID字符串</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IIdGeneratorService.Reset(System.Int32)">
            <summary>
            重置ID生成器
            </summary>
            <param name="startId">起始ID</param>
        </member>
        <member name="P:TiaXmlGenerator.Services.Interfaces.IIdGeneratorService.CurrentId">
            <summary>
            获取当前ID值
            </summary>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.IFileService">
            <summary>
            文件服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IFileService.FileExists(System.String)">
            <summary>
            检查文件是否存在
            </summary>
            <param name="filePath">文件路径</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IFileService.ReadAllLinesAsync(System.String)">
            <summary>
            读取文件所有行
            </summary>
            <param name="filePath">文件路径</param>
            <returns>文件行列表</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IFileService.WriteAllLinesAsync(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            写入文件所有行
            </summary>
            <param name="filePath">文件路径</param>
            <param name="lines">行列表</param>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IFileService.ReadLinesRangeAsync(System.String,System.Int32,System.Int32)">
            <summary>
            读取文件指定行范围
            </summary>
            <param name="filePath">文件路径</param>
            <param name="startLine">起始行（1-based）</param>
            <param name="endLine">结束行（1-based，包含）</param>
            <returns>指定范围的行列表</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IFileService.CreateBackup(System.String)">
            <summary>
            创建文件备份
            </summary>
            <param name="filePath">原文件路径</param>
            <returns>备份文件路径</returns>
        </member>
        <member name="T:TiaXmlGenerator.Services.Interfaces.IXmlProcessorService">
            <summary>
            XML处理服务接口
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IXmlProcessorService.LoadXmlDocumentAsync(System.String)">
            <summary>
            加载XML文档
            </summary>
            <param name="filePath">XML文件路径</param>
            <returns>XML文档</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IXmlProcessorService.SaveXmlDocumentAsync(System.Xml.Linq.XDocument,System.String)">
            <summary>
            保存XML文档
            </summary>
            <param name="document">XML文档</param>
            <param name="filePath">保存路径</param>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IXmlProcessorService.FixXmlFormat(System.String)">
            <summary>
            修复XML格式问题
            </summary>
            <param name="xmlContent">XML内容</param>
            <returns>修复后的XML内容</returns>
        </member>
        <member name="M:TiaXmlGenerator.Services.Interfaces.IXmlProcessorService.ValidateXmlFormat(System.String)">
            <summary>
            验证XML格式
            </summary>
            <param name="xmlContent">XML内容</param>
            <returns>是否有效</returns>
        </member>
    </members>
</doc>
