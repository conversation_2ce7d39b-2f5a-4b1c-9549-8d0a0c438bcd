<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyTitle>TIA XML Generator Services</AssemblyTitle>
    <AssemblyDescription>Service interfaces for TIA Portal XML Generator</AssemblyDescription>
    <AssemblyCompany>TIA XML Generator</AssemblyCompany>
    <AssemblyProduct>TIA XML Generator</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TiaXmlGenerator.Models\TiaXmlGenerator.Models.csproj" />
  </ItemGroup>

</Project>
