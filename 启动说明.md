# TIA Portal XML Generator 启动说明

## 项目概述

这是一个基于 .NET 8 和 WPF 的桌面应用程序，用于生成 TIA Portal XML 文件。

## 系统要求

- Windows 10/11 (推荐)
- .NET 8.0 Runtime 或 SDK
- Visual Studio 2022 (可选，用于开发)

## 启动方式

### 方式一：使用 Visual Studio（推荐）

1. 打开 Visual Studio 2022
2. 打开解决方案文件：`TiaXmlGenerator.sln`
3. 在解决方案资源管理器中，右键点击 `TiaXmlGenerator.UI` 项目
4. 选择 **"设为启动项目"**
5. 按 `F5` 或点击 **"开始调试"** 按钮
6. 应用程序将自动构建并启动

### 方式二：使用命令行

在项目根目录下打开命令提示符或 PowerShell：

```bash
# 构建项目
dotnet build TiaXmlGenerator.sln

# 运行应用程序
dotnet run --project TiaXmlGenerator.UI
```

### 方式三：使用启动脚本

#### Windows 用户：
双击运行 `start_app.bat` 文件

#### Linux/Mac 用户：
```bash
chmod +x start_app.sh
./start_app.sh
```

### 方式四：直接运行可执行文件

1. 确保项目已构建：
   ```bash
   dotnet build TiaXmlGenerator.sln
   ```

2. 导航到输出目录：
   ```bash
   cd TiaXmlGenerator.UI/bin/Debug/net8.0-windows
   ```

3. 运行可执行文件：
   ```bash
   TiaXmlGenerator.UI.exe
   ```

## 项目结构

```
TiaXmlGenerator/
├── TiaXmlGenerator.sln          # 解决方案文件
├── TiaXmlGenerator.UI/          # 主启动项目（WPF界面）
├── TiaXmlGenerator.Core/        # 核心业务逻辑
├── TiaXmlGenerator.Services/    # 服务层
├── TiaXmlGenerator.Models/      # 数据模型
├── start_app.bat               # Windows启动脚本
├── start_app.sh                # Linux/Mac启动脚本
└── 启动说明.md                 # 本文档
```

## 启动入口配置

### 主要配置文件：

1. **App.xaml** - WPF应用程序入口
   - 启动窗口：`Views/MainWindow.xaml`
   - 应用程序类：`TiaXmlGenerator.UI.App`

2. **TiaXmlGenerator.UI.csproj** - 项目配置
   - 输出类型：`WinExe` (Windows可执行文件)
   - 目标框架：`net8.0-windows`
   - 使用WPF：`true`

3. **App.xaml.cs** - 应用程序启动逻辑
   - 依赖注入配置
   - 服务注册
   - 主窗口显示

## 依赖注入配置

应用程序使用 Microsoft.Extensions.Hosting 进行依赖注入：

- **核心服务**：
  - `IIdGeneratorService` → `IdGenerator`
  - `IExcelReaderService` → `ExcelReaderService`
  - `INetworkGeneratorService` → `NetworkGenerator`
  - `IFileService` → `FileService`
  - `IXmlProcessorService` → `XmlProcessorService`
  - `IXmlGeneratorService` → `XmlGenerator`

- **UI服务**：
  - `MainViewModel`
  - `MainWindow`

## 故障排除

### 常见问题：

1. **构建失败**
   - 确保安装了 .NET 8.0 SDK
   - 运行 `dotnet restore` 恢复包

2. **应用程序无法启动**
   - 检查是否在 Windows 环境下运行
   - 确保所有依赖项已正确安装

3. **缺少文件错误**
   - 确保项目完整构建
   - 检查输出目录是否包含所有必要文件

### 调试模式：

使用 Visual Studio 的调试功能可以：
- 设置断点
- 查看变量值
- 跟踪执行流程
- 查看详细错误信息

## 使用说明

应用程序启动后，您将看到一个图形界面，包含：

1. **文件设置区域**：
   - Excel文件路径
   - 模板XML文件路径
   - 输出XML文件路径

2. **操作区域**：
   - 生成XML文件按钮
   - 清空日志按钮
   - 状态显示

3. **日志显示区域**：
   - 实时显示处理过程
   - 错误信息提示

## 开发环境设置

如果您需要修改或扩展功能：

1. 安装 Visual Studio 2022 with .NET 8.0 workload
2. 安装 Git (用于版本控制)
3. 克隆或下载项目源代码
4. 打开 `TiaXmlGenerator.sln` 开始开发

---

如有问题，请检查日志输出或联系开发团队。
