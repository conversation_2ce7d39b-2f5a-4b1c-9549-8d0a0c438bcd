<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TiaXmlGenerator.Core</name>
    </assembly>
    <members>
        <member name="T:TiaXmlGenerator.Core.IdGenerator">
            <summary>
            ID生成器，对应Python中的IdGenerator类
            </summary>
        </member>
        <member name="P:TiaXmlGenerator.Core.IdGenerator.CurrentId">
            <summary>
            当前ID值
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.#ctor(Microsoft.Extensions.Logging.ILogger{TiaXmlGenerator.Core.IdGenerator},System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
            <param name="startId">起始ID，默认为0x13</param>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.GetNextId">
            <summary>
            获取下一个ID
            </summary>
            <returns>十六进制ID字符串</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.Reset(System.Int32)">
            <summary>
            重置ID生成器
            </summary>
            <param name="startId">起始ID</param>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.GetNextIds(System.Int32)">
            <summary>
            获取指定数量的连续ID
            </summary>
            <param name="count">ID数量</param>
            <returns>ID列表</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.PeekNextId">
            <summary>
            预览下一个ID（不消耗ID）
            </summary>
            <returns>下一个ID的十六进制字符串</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.Skip(System.Int32)">
            <summary>
            跳过指定数量的ID
            </summary>
            <param name="count">跳过的数量</param>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.IsValidId(System.Int32)">
            <summary>
            检查ID是否在有效范围内
            </summary>
            <param name="id">要检查的ID</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.ParseHexId(System.String)">
            <summary>
            将十六进制字符串转换为整数ID
            </summary>
            <param name="hexId">十六进制ID字符串</param>
            <returns>整数ID，转换失败返回-1</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.ToHexId(System.Int32)">
            <summary>
            将整数ID转换为十六进制字符串
            </summary>
            <param name="id">整数ID</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:TiaXmlGenerator.Core.IdGenerator.GetStatus">
            <summary>
            获取ID生成器状态信息
            </summary>
            <returns>状态信息</returns>
        </member>
        <member name="T:TiaXmlGenerator.Core.XmlGenerator">
            <summary>
            XML生成器核心类，对应Python中的XMLGenerator类
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateXmlAsync(TiaXmlGenerator.Models.GenerationConfig)">
            <summary>
            生成XML文件 - 严格按照Python代码的generate_xml方法实现
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateXmlUsingTextProcessing(TiaXmlGenerator.Models.GenerationConfig,TiaXmlGenerator.Models.ExcelReadResult)">
            <summary>
            使用基于文本处理的方式生成XML，严格按照Python代码实现
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateInstanceDeclarations(System.String[],System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData},System.Collections.Generic.List{System.String})">
            <summary>
            生成实例声明，对应Python代码中的实例声明生成部分
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateFixedVariableDeclarations(System.String[],System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData},System.Collections.Generic.List{System.String})">
            <summary>
            生成固定变量声明，对应Python代码中的固定变量声明部分
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateInstanceNetworks(System.String[],System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData},System.String,System.Collections.Generic.List{System.String})">
            <summary>
            生成各实例的网络代码，对应Python代码中的实例网络生成部分
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.CustomizeNetworkContent(System.String,TiaXmlGenerator.Models.InstanceData,System.Int32)">
            <summary>
            自定义网络模板中的参数，对应Python代码中的_customize_network_content方法
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.UpdateNetworkIds(System.String,System.Int32)">
            <summary>
            更新网络中的ID属性，为每个实例分配唯一ID，对应Python代码中的_update_network_ids方法
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.ValidateInputFilesAsync(TiaXmlGenerator.Models.GenerationConfig)">
            <summary>
            验证输入文件
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateStaticMembers(System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData})">
            <summary>
            生成静态成员变量
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.FindStaticSection">
            <summary>
            查找Static段
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateInstanceDeclarations(System.Xml.Linq.XElement,System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData})">
            <summary>
            生成实例声明
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateFixedMembers(System.Xml.Linq.XElement)">
            <summary>
            生成固定成员
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.GenerateNetworks(System.Collections.Generic.List{TiaXmlGenerator.Models.InstanceData},TiaXmlGenerator.Models.OpModeData)">
            <summary>
            生成网络代码
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.FindNetworkSourceElement">
            <summary>
            查找NetworkSource元素
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.FixMultilingualTextIds(System.Boolean)">
            <summary>
            修复多语言文本ID冲突
            </summary>
        </member>
        <member name="M:TiaXmlGenerator.Core.XmlGenerator.ShouldPreserveId(System.Xml.Linq.XElement,System.String)">
            <summary>
            判断是否应该保留ID
            </summary>
        </member>
    </members>
</doc>
