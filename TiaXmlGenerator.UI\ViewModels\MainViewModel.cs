using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows.Input;
using TiaXmlGenerator.Models;
using TiaXmlGenerator.Services.Interfaces;

namespace TiaXmlGenerator.UI.ViewModels
{
    /// <summary>
    /// 主窗口视图模型，对应Python中的XmlGeneratorUI类
    /// </summary>
    public partial class MainViewModel : ObservableObject
    {
        private readonly IXmlGeneratorService _xmlGenerator;
        private readonly ILogger<MainViewModel> _logger;

        [ObservableProperty]
        private string _excelFilePath = string.Empty;

        [ObservableProperty]
        private string _templateFilePath = string.Empty;

        [ObservableProperty]
        private string _outputFilePath = string.Empty;

        [ObservableProperty]
        private int _progressValue = 0;

        [ObservableProperty]
        private string _statusMessage = "就绪";

        [ObservableProperty]
        private bool _isGenerating = false;

        [ObservableProperty]
        private string _currentTime = string.Empty;

        [ObservableProperty]
        private string _logText = string.Empty;

        public ObservableCollection<string> LogMessages { get; } = new();

        public ICommand BrowseExcelCommand { get; }
        public ICommand BrowseTemplateCommand { get; }
        public ICommand BrowseOutputCommand { get; }
        public ICommand GenerateXmlCommand { get; }
        public ICommand ClearLogCommand { get; }

        public MainViewModel(IXmlGeneratorService xmlGenerator, ILogger<MainViewModel> logger)
        {
            _xmlGenerator = xmlGenerator;
            _logger = logger;

            // 初始化命令
            BrowseExcelCommand = new RelayCommand(BrowseExcelFile);
            BrowseTemplateCommand = new RelayCommand(BrowseTemplateFile);
            BrowseOutputCommand = new RelayCommand(BrowseOutputFile);
            GenerateXmlCommand = new AsyncRelayCommand(GenerateXmlAsync, CanGenerateXml);
            ClearLogCommand = new RelayCommand(ClearLog);

            // 订阅XML生成器事件
            _xmlGenerator.LogMessage += OnXmlGeneratorLogMessage;
            _xmlGenerator.ProgressChanged += OnXmlGeneratorProgressChanged;
            _xmlGenerator.StatusChanged += OnXmlGeneratorStatusChanged;

            // 初始化默认路径
            InitializeDefaultPaths();

            // 启动时间更新
            StartTimeUpdate();

            // 添加欢迎消息
            LogMessages.Add("欢迎使用TIA Portal XML生成器!");
            LogMessages.Add("请设置文件路径并点击 [生成XML文件] 按钮开始处理");
        }

        /// <summary>
        /// 初始化默认路径
        /// </summary>
        private void InitializeDefaultPaths()
        {
            var currentDir = Directory.GetCurrentDirectory();
            ExcelFilePath = Path.Combine(currentDir, "manualRow_2.xlsx");
            TemplateFilePath = Path.Combine(currentDir, "template2.xml");
            OutputFilePath = Path.Combine(currentDir, "output_new.xml");
        }

        /// <summary>
        /// 启动时间更新
        /// </summary>
        private void StartTimeUpdate()
        {
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();
            CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 浏览Excel文件
        /// </summary>
        private void BrowseExcelFile()
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择Excel文件",
                Filter = "Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|所有文件 (*.*)|*.*",
                DefaultExt = "xlsx"
            };

            if (dialog.ShowDialog() == true)
            {
                ExcelFilePath = dialog.FileName;
                _logger.LogInformation("选择Excel文件: {FilePath}", ExcelFilePath);
            }
        }

        /// <summary>
        /// 浏览模板文件
        /// </summary>
        private void BrowseTemplateFile()
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择模板XML文件",
                Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (dialog.ShowDialog() == true)
            {
                TemplateFilePath = dialog.FileName;
                _logger.LogInformation("选择模板文件: {FilePath}", TemplateFilePath);
            }
        }

        /// <summary>
        /// 浏览输出文件
        /// </summary>
        private void BrowseOutputFile()
        {
            var dialog = new SaveFileDialog
            {
                Title = "选择输出XML文件",
                Filter = "XML文件 (*.xml)|*.xml|所有文件 (*.*)|*.*",
                DefaultExt = "xml"
            };

            if (dialog.ShowDialog() == true)
            {
                OutputFilePath = dialog.FileName;
                _logger.LogInformation("选择输出文件: {FilePath}", OutputFilePath);
            }
        }

        /// <summary>
        /// 检查是否可以生成XML
        /// </summary>
        private bool CanGenerateXml()
        {
            return !IsGenerating &&
                   !string.IsNullOrWhiteSpace(ExcelFilePath) &&
                   !string.IsNullOrWhiteSpace(TemplateFilePath) &&
                   !string.IsNullOrWhiteSpace(OutputFilePath);
        }

        /// <summary>
        /// 生成XML文件
        /// </summary>
        private async Task GenerateXmlAsync()
        {
            try
            {
                // 验证文件存在性
                if (!File.Exists(ExcelFilePath))
                {
                    AddLogMessage($"错误：Excel文件不存在: {ExcelFilePath}");
                    return;
                }

                if (!File.Exists(TemplateFilePath))
                {
                    AddLogMessage($"错误：模板文件不存在: {TemplateFilePath}");
                    return;
                }

                // 清空日志并显示开始消息
                LogMessages.Clear();
                AddLogMessage($"=== {DateTime.Now:yyyy-MM-dd HH:mm:ss} 开始生成XML ===");
                AddLogMessage("");

                // 更新UI状态
                IsGenerating = true;
                ProgressValue = 10;
                StatusMessage = "正在处理...";

                // 创建生成配置
                var config = new GenerationConfig
                {
                    ExcelFilePath = ExcelFilePath,
                    TemplateFilePath = TemplateFilePath,
                    OutputFilePath = OutputFilePath
                };

                // 生成XML
                var success = await _xmlGenerator.GenerateXmlAsync(config);

                // 更新UI状态
                if (success)
                {
                    ProgressValue = 100;
                    StatusMessage = "处理完成";
                    AddLogMessage("");
                    AddLogMessage($"=== {DateTime.Now:yyyy-MM-dd HH:mm:ss} 生成成功! ===");
                    AddLogMessage($"文件已保存至: {OutputFilePath}");

                    // 显示成功消息
                    System.Windows.MessageBox.Show(
                        $"XML生成成功!\n文件已保存至:\n{OutputFilePath}",
                        "成功",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    ProgressValue = 0;
                    StatusMessage = "处理失败";
                    AddLogMessage("");
                    AddLogMessage($"=== {DateTime.Now:yyyy-MM-dd HH:mm:ss} 生成失败! ===");
                    AddLogMessage("请检查日志获取详细信息。");

                    // 显示错误消息
                    System.Windows.MessageBox.Show(
                        "XML生成失败，请检查日志获取详细信息。",
                        "错误",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成XML时发生异常");

                ProgressValue = 0;
                StatusMessage = "处理出错";
                AddLogMessage("");
                AddLogMessage($"=== {DateTime.Now:yyyy-MM-dd HH:mm:ss} 错误! ===");
                AddLogMessage($"错误信息: {ex.Message}");

                System.Windows.MessageBox.Show(
                    $"处理时出错:\n{ex.Message}",
                    "错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsGenerating = false;
                // 刷新命令状态
                ((AsyncRelayCommand)GenerateXmlCommand).NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLog()
        {
            LogMessages.Clear();
            LogText = string.Empty;
            _logger.LogInformation("日志已清空");
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        private void AddLogMessage(string message)
        {
            // 确保在UI线程中执行
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                LogMessages.Add(message);
                LogText += message + Environment.NewLine;
            }
            else
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    LogMessages.Add(message);
                    LogText += message + Environment.NewLine;
                });
            }
        }

        #region XML生成器事件处理

        private void OnXmlGeneratorLogMessage(object? sender, string message)
        {
            AddLogMessage(message);
        }

        private void OnXmlGeneratorProgressChanged(object? sender, int progress)
        {
            // 确保在UI线程中执行
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                ProgressValue = progress;
            }
            else
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() => ProgressValue = progress);
            }
        }

        private void OnXmlGeneratorStatusChanged(object? sender, string status)
        {
            // 确保在UI线程中执行
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                StatusMessage = status;
            }
            else
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() => StatusMessage = status);
            }
        }

        #endregion

        /// <summary>
        /// 清理资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消订阅事件
                _xmlGenerator.LogMessage -= OnXmlGeneratorLogMessage;
                _xmlGenerator.ProgressChanged -= OnXmlGeneratorProgressChanged;
                _xmlGenerator.StatusChanged -= OnXmlGeneratorStatusChanged;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
