using System.Windows;
using TiaXmlGenerator.UI.ViewModels;

namespace TiaXmlGenerator.UI.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow(MainViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // 设置窗口属性
            Title = "TIA Portal XML生成器 v1.0";
            
            // 窗口关闭时清理资源
            Closing += MainWindow_Closing;
        }

        private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            // 清理ViewModel资源
            if (DataContext is MainViewModel viewModel)
            {
                viewModel.Dispose();
            }
        }
    }
}
