using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Windows;
using TiaXmlGenerator.Core;
using TiaXmlGenerator.Services;
using TiaXmlGenerator.Services.Interfaces;
using TiaXmlGenerator.UI.ViewModels;
using TiaXmlGenerator.UI.Views;

namespace TiaXmlGenerator.UI
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 创建主机构建器
            var hostBuilder = Host.CreateDefaultBuilder()
                .ConfigureServices(ConfigureServices);

            _host = hostBuilder.Build();

            // 启动主机
            _host.Start();

            // 显示主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }

        /// <summary>
        /// 配置依赖注入服务
        /// </summary>
        private void ConfigureServices(IServiceCollection services)
        {
            // 配置日志
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 注册核心服务
            services.AddSingleton<IIdGeneratorService, IdGenerator>();
            services.AddScoped<IExcelReaderService, ExcelReaderService>();
            services.AddScoped<INetworkGeneratorService, NetworkGenerator>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IXmlProcessorService, XmlProcessorService>();
            services.AddScoped<IXmlGeneratorService, XmlGenerator>();

            // 注册UI相关服务
            services.AddTransient<MainViewModel>();
            services.AddTransient<MainWindow>();

            // 配置其他服务
            ConfigureAdditionalServices(services);
        }

        /// <summary>
        /// 配置额外的服务
        /// </summary>
        private void ConfigureAdditionalServices(IServiceCollection services)
        {
            // 这里可以添加其他配置，如：
            // - 配置文件服务
            // - 缓存服务
            // - 其他业务服务等
        }
    }
}

namespace TiaXmlGenerator.Services
{
    /// <summary>
    /// 文件服务实现
    /// </summary>
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;

        public FileService(ILogger<FileService> logger)
        {
            _logger = logger;
        }

        public bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }

        public async Task<List<string>> ReadAllLinesAsync(string filePath)
        {
            try
            {
                var lines = await File.ReadAllLinesAsync(filePath);
                return lines.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取文件失败: {FilePath}", filePath);
                return new List<string>();
            }
        }

        public async Task WriteAllLinesAsync(string filePath, IEnumerable<string> lines)
        {
            try
            {
                await File.WriteAllLinesAsync(filePath, lines);
                _logger.LogInformation("文件写入成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入文件失败: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<List<string>> ReadLinesRangeAsync(string filePath, int startLine, int endLine)
        {
            try
            {
                var allLines = await File.ReadAllLinesAsync(filePath);
                var startIndex = Math.Max(0, startLine - 1); // 转换为0-based索引
                var endIndex = Math.Min(allLines.Length, endLine); // 确保不超出范围

                if (startIndex >= allLines.Length || startIndex >= endIndex)
                {
                    return new List<string>();
                }

                var result = new List<string>();
                for (int i = startIndex; i < endIndex; i++)
                {
                    result.Add(allLines[i]);
                }

                _logger.LogDebug("读取文件行范围 {StartLine}-{EndLine}: {Count}行", startLine, endLine, result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取文件行范围失败: {FilePath} ({StartLine}-{EndLine})", filePath, startLine, endLine);
                return new List<string>();
            }
        }

        public string CreateBackup(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("备份文件不存在: {FilePath}", filePath);
                    return string.Empty;
                }

                var backupPath = $"{filePath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                File.Copy(filePath, backupPath);
                _logger.LogInformation("创建备份文件: {BackupPath}", backupPath);
                return backupPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建备份文件失败: {FilePath}", filePath);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// XML处理服务实现
    /// </summary>
    public class XmlProcessorService : IXmlProcessorService
    {
        private readonly ILogger<XmlProcessorService> _logger;

        public XmlProcessorService(ILogger<XmlProcessorService> logger)
        {
            _logger = logger;
        }

        public async Task<System.Xml.Linq.XDocument> LoadXmlDocumentAsync(string filePath)
        {
            try
            {
                var content = await File.ReadAllTextAsync(filePath);
                var document = System.Xml.Linq.XDocument.Parse(content);
                _logger.LogInformation("XML文档加载成功: {FilePath}", filePath);
                return document;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载XML文档失败: {FilePath}", filePath);
                throw;
            }
        }

        public async Task SaveXmlDocumentAsync(System.Xml.Linq.XDocument document, string filePath)
        {
            try
            {
                await using var stream = File.Create(filePath);
                await document.SaveAsync(stream, System.Xml.Linq.SaveOptions.None, CancellationToken.None);
                _logger.LogInformation("XML文档保存成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存XML文档失败: {FilePath}", filePath);
                throw;
            }
        }

        public string FixXmlFormat(string xmlContent)
        {
            try
            {
                // 这里实现XML格式修复逻辑
                // 对应Python中的_fix_template_format方法
                _logger.LogDebug("修复XML格式");
                return xmlContent; // 暂时返回原内容，具体实现需要根据需求完成
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修复XML格式失败");
                return xmlContent;
            }
        }

        public bool ValidateXmlFormat(string xmlContent)
        {
            try
            {
                System.Xml.Linq.XDocument.Parse(xmlContent);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// 网络生成器实现（简化版本）
    /// </summary>
    public class NetworkGenerator : INetworkGeneratorService
    {
        private readonly ILogger<NetworkGenerator> _logger;
        private readonly IIdGeneratorService _idGenerator;
        private readonly IFileService _fileService;
        private readonly string _templateFilePath;

        public NetworkGenerator(
            ILogger<NetworkGenerator> logger,
            IIdGeneratorService idGenerator,
            IFileService fileService)
        {
            _logger = logger;
            _idGenerator = idGenerator;
            _fileService = fileService;
            _templateFilePath = "template2.xml"; // 默认模板路径
        }

        public List<string> GenerateAllNetworks(List<TiaXmlGenerator.Models.InstanceData> instances, TiaXmlGenerator.Models.OpModeData opModeData)
        {
            var allNetworkLines = new List<string>();

            // 1. 添加固定网络部分
            var fixedPart = GenerateFixedNetworksPart();
            allNetworkLines.AddRange(fixedPart);

            // 2. 添加实例网络
            for (int i = 0; i < instances.Count; i++)
            {
                var isLast = (i == instances.Count - 1);
                var instanceNetwork = GenerateInstanceNetwork(instances[i], opModeData, isLast);
                allNetworkLines.AddRange(instanceNetwork);
            }

            _logger.LogInformation("生成网络代码完成，共 {Count} 行", allNetworkLines.Count);
            return allNetworkLines;
        }

        public List<string> GenerateFixedNetworksPart()
        {
            _logger.LogInformation("生成固定网络部分（模板行 3077-3459）");
            // 这里需要实现从模板文件读取固定部分的逻辑
            return new List<string> { "<!-- 固定网络部分 -->" };
        }

        public List<string> GenerateInstanceNetwork(TiaXmlGenerator.Models.InstanceData instanceData, TiaXmlGenerator.Models.OpModeData opModeData, bool isLastInstance = false)
        {
            _logger.LogInformation("生成实例网络: {InstanceName}, 是否最后一个: {IsLast}", instanceData.Name, isLastInstance);

            // 这里需要实现实例网络生成逻辑
            var lines = new List<string>
            {
                $"<!-- 实例网络: {instanceData.Name} -->"
            };

            if (isLastInstance)
            {
                lines.Add("<!-- 最后一个实例的特殊处理 -->");
            }

            return lines;
        }
    }
}
