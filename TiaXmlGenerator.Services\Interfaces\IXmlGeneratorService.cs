using System;
using System.Threading.Tasks;
using TiaXmlGenerator.Models;

namespace TiaXmlGenerator.Services.Interfaces
{
    /// <summary>
    /// XML生成服务接口
    /// </summary>
    public interface IXmlGeneratorService
    {
        /// <summary>
        /// 生成XML文件
        /// </summary>
        /// <param name="config">生成配置</param>
        /// <returns>是否成功</returns>
        Task<bool> GenerateXmlAsync(GenerationConfig config);

        /// <summary>
        /// 日志消息事件
        /// </summary>
        event EventHandler<string> LogMessage;

        /// <summary>
        /// 进度变化事件
        /// </summary>
        event EventHandler<int> ProgressChanged;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        event EventHandler<string> StatusChanged;
    }

    /// <summary>
    /// Excel读取服务接口
    /// </summary>
    public interface IExcelReaderService
    {
        /// <summary>
        /// 读取Excel数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>读取结果</returns>
        Task<ExcelReadResult> ReadExcelDataAsync(string filePath);
    }

    /// <summary>
    /// 网络生成服务接口
    /// </summary>
    public interface INetworkGeneratorService
    {
        /// <summary>
        /// 生成所有网络代码
        /// </summary>
        /// <param name="instances">实例列表</param>
        /// <param name="opModeData">OpMode数据</param>
        /// <returns>网络代码行列表</returns>
        List<string> GenerateAllNetworks(List<InstanceData> instances, OpModeData opModeData);

        /// <summary>
        /// 生成固定网络部分
        /// </summary>
        /// <returns>固定网络代码行列表</returns>
        List<string> GenerateFixedNetworksPart();

        /// <summary>
        /// 生成实例网络
        /// </summary>
        /// <param name="instanceData">实例数据</param>
        /// <param name="opModeData">OpMode数据</param>
        /// <param name="isLastInstance">是否为最后一个实例</param>
        /// <returns>实例网络代码行列表</returns>
        List<string> GenerateInstanceNetwork(InstanceData instanceData, OpModeData opModeData, bool isLastInstance = false);
    }

    /// <summary>
    /// ID生成服务接口
    /// </summary>
    public interface IIdGeneratorService
    {
        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>十六进制ID字符串</returns>
        string GetNextId();

        /// <summary>
        /// 重置ID生成器
        /// </summary>
        /// <param name="startId">起始ID</param>
        void Reset(int startId = 0x13);

        /// <summary>
        /// 获取当前ID值
        /// </summary>
        int CurrentId { get; }
    }

    /// <summary>
    /// 文件服务接口
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否存在</returns>
        bool FileExists(string filePath);

        /// <summary>
        /// 读取文件所有行
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件行列表</returns>
        Task<List<string>> ReadAllLinesAsync(string filePath);

        /// <summary>
        /// 写入文件所有行
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="lines">行列表</param>
        Task WriteAllLinesAsync(string filePath, IEnumerable<string> lines);

        /// <summary>
        /// 读取文件指定行范围
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startLine">起始行（1-based）</param>
        /// <param name="endLine">结束行（1-based，包含）</param>
        /// <returns>指定范围的行列表</returns>
        Task<List<string>> ReadLinesRangeAsync(string filePath, int startLine, int endLine);

        /// <summary>
        /// 创建文件备份
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <returns>备份文件路径</returns>
        string CreateBackup(string filePath);
    }

    /// <summary>
    /// XML处理服务接口
    /// </summary>
    public interface IXmlProcessorService
    {
        /// <summary>
        /// 加载XML文档
        /// </summary>
        /// <param name="filePath">XML文件路径</param>
        /// <returns>XML文档</returns>
        Task<System.Xml.Linq.XDocument> LoadXmlDocumentAsync(string filePath);

        /// <summary>
        /// 保存XML文档
        /// </summary>
        /// <param name="document">XML文档</param>
        /// <param name="filePath">保存路径</param>
        Task SaveXmlDocumentAsync(System.Xml.Linq.XDocument document, string filePath);

        /// <summary>
        /// 修复XML格式问题
        /// </summary>
        /// <param name="xmlContent">XML内容</param>
        /// <returns>修复后的XML内容</returns>
        string FixXmlFormat(string xmlContent);

        /// <summary>
        /// 验证XML格式
        /// </summary>
        /// <param name="xmlContent">XML内容</param>
        /// <returns>是否有效</returns>
        bool ValidateXmlFormat(string xmlContent);
    }
}
