using Microsoft.Extensions.Logging;
using TiaXmlGenerator.Services.Interfaces;

namespace TiaXmlGenerator.Core
{
    /// <summary>
    /// ID生成器，对应Python中的IdGenerator类
    /// </summary>
    public class IdGenerator : IIdGeneratorService
    {
        private int _currentId;
        private readonly ILogger<IdGenerator> _logger;

        /// <summary>
        /// 当前ID值
        /// </summary>
        public int CurrentId => _currentId;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="startId">起始ID，默认为0x13</param>
        public IdGenerator(ILogger<IdGenerator> logger, int startId = 0x13)
        {
            _logger = logger;
            _currentId = startId;
            _logger.LogDebug("IdGenerator initialized with start ID: {StartId:X}", startId);
        }

        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>十六进制ID字符串</returns>
        public string GetNextId()
        {
            var nextId = _currentId;
            _currentId++;
            var hexId = nextId.ToString("X");
            
            _logger.LogTrace("Generated ID: {HexId} (Decimal: {DecimalId})", hexId, nextId);
            return hexId;
        }

        /// <summary>
        /// 重置ID生成器
        /// </summary>
        /// <param name="startId">起始ID</param>
        public void Reset(int startId = 0x13)
        {
            var oldId = _currentId;
            _currentId = startId;
            _logger.LogInformation("IdGenerator reset from {OldId:X} to {NewId:X}", oldId, startId);
        }

        /// <summary>
        /// 获取指定数量的连续ID
        /// </summary>
        /// <param name="count">ID数量</param>
        /// <returns>ID列表</returns>
        public List<string> GetNextIds(int count)
        {
            var ids = new List<string>(count);
            for (int i = 0; i < count; i++)
            {
                ids.Add(GetNextId());
            }
            return ids;
        }

        /// <summary>
        /// 预览下一个ID（不消耗ID）
        /// </summary>
        /// <returns>下一个ID的十六进制字符串</returns>
        public string PeekNextId()
        {
            return _currentId.ToString("X");
        }

        /// <summary>
        /// 跳过指定数量的ID
        /// </summary>
        /// <param name="count">跳过的数量</param>
        public void Skip(int count)
        {
            var oldId = _currentId;
            _currentId += count;
            _logger.LogDebug("Skipped {Count} IDs from {OldId:X} to {NewId:X}", count, oldId, _currentId);
        }

        /// <summary>
        /// 检查ID是否在有效范围内
        /// </summary>
        /// <param name="id">要检查的ID</param>
        /// <returns>是否有效</returns>
        public bool IsValidId(int id)
        {
            // 通常TIA Portal的ID范围是有限的，这里可以根据实际需求调整
            return id >= 0 && id <= 0xFFFF;
        }

        /// <summary>
        /// 将十六进制字符串转换为整数ID
        /// </summary>
        /// <param name="hexId">十六进制ID字符串</param>
        /// <returns>整数ID，转换失败返回-1</returns>
        public static int ParseHexId(string hexId)
        {
            if (string.IsNullOrWhiteSpace(hexId))
                return -1;

            if (int.TryParse(hexId, System.Globalization.NumberStyles.HexNumber, null, out int result))
                return result;

            return -1;
        }

        /// <summary>
        /// 将整数ID转换为十六进制字符串
        /// </summary>
        /// <param name="id">整数ID</param>
        /// <returns>十六进制字符串</returns>
        public static string ToHexId(int id)
        {
            return id.ToString("X");
        }

        /// <summary>
        /// 获取ID生成器状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public string GetStatus()
        {
            return $"Current ID: {_currentId:X} (Decimal: {_currentId})";
        }
    }
}
