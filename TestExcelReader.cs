using Microsoft.Extensions.Logging;
using TiaXmlGenerator.Services;
using OfficeOpenXml;

class TestExcelReader
{
    static async Task Main(string[] args)
    {
        // 设置EPPlus许可证
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug);
        });
        
        var logger = loggerFactory.CreateLogger<ExcelReaderService>();
        var excelReader = new ExcelReaderService(logger);
        
        // 测试文件路径
        var testFiles = new[]
        {
            "test_data.csv",
            "manualRow_2.xlsx",
            "test_data.xlsx"
        };
        
        foreach (var testFile in testFiles)
        {
            if (File.Exists(testFile))
            {
                Console.WriteLine($"\n=== 测试文件: {testFile} ===");
                try
                {
                    var result = await excelReader.ReadExcelDataAsync(testFile);
                    Console.WriteLine($"读取结果: {result.Instances.Count} 个实例");
                    Console.WriteLine($"OpMode注释: '{result.OpModeComment}'");
                    
                    foreach (var instance in result.Instances)
                    {
                        Console.WriteLine($"  实例: {instance.Name} - {instance.Comment}");
                        Console.WriteLine($"    I点: {instance.ForwardSensorAddr}, {instance.BackwardSensorAddr}");
                        Console.WriteLine($"    Q点: {instance.ForwardOutputAddr}, {instance.BackwardOutputAddr}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"读取失败: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine($"文件不存在: {testFile}");
            }
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
