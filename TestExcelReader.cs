using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using TiaXmlGenerator.Services;
using System;
using System.IO;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        // 设置EPPlus许可证
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        // 创建控制台日志记录器
        using var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug);
        });
        
        var logger = loggerFactory.CreateLogger<ExcelReaderService>();
        var excelReader = new ExcelReaderService(logger);
        
        // 测试Excel文件路径
        var excelPath = "manualRow_2.xlsx";
        
        Console.WriteLine($"测试Excel文件: {excelPath}");
        Console.WriteLine($"文件存在: {File.Exists(excelPath)}");
        
        if (File.Exists(excelPath))
        {
            var fileInfo = new FileInfo(excelPath);
            Console.WriteLine($"文件大小: {fileInfo.Length} 字节");
            Console.WriteLine($"最后修改时间: {fileInfo.LastWriteTime}");
        }
        
        try
        {
            Console.WriteLine("开始读取Excel文件...");
            var result = await excelReader.ReadExcelDataAsync(excelPath);
            
            Console.WriteLine($"读取结果:");
            Console.WriteLine($"  实例数量: {result.Instances?.Count ?? 0}");
            Console.WriteLine($"  OpMode注释: '{result.OpModeComment}'");
            
            if (result.Instances != null && result.Instances.Count > 0)
            {
                Console.WriteLine("实例列表:");
                for (int i = 0; i < result.Instances.Count; i++)
                {
                    var instance = result.Instances[i];
                    Console.WriteLine($"  {i + 1}. {instance.Name} - {instance.Comment} ({instance.Type})");
                }
            }
            else
            {
                Console.WriteLine("没有读取到任何实例数据");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取Excel时发生错误: {ex.Message}");
            Console.WriteLine($"异常类型: {ex.GetType().Name}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
        
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
