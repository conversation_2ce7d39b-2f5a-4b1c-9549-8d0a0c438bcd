using OfficeOpenXml;
using System;
using System.IO;

namespace ExcelCreator;

class Program
{
    static void Main()
    {
        // 设置EPPlus许可证
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        // 创建Excel文件
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");

        // 设置表头 - 完全按照用户提供的格式
        var headers = new[]
        {
            "手动行号", "OpMode名称", "OpMode注释", "OpMode编号", "手动行DB块名称", "手动FB块编号",
            "实例名称", "实例注释", "I点-工作位1", "I点-工作位1偏移量", "I点-工作位2", "I点-工作位2偏移量",
            "I点-原位1", "I点-原位1偏移量", "I点-原位2", "I点-原位2偏移量", "Q点-工作位", "Q点-工作位偏移量",
            "Q点-原位", "Q点-原位偏移量"
        };

        // 写入表头
        for (int col = 0; col < headers.Length; col++)
        {
            worksheet.Cells[1, col + 1].Value = headers[col];
        }

        // 写入测试数据 - 完全按照用户提供的格式
        var testData = new object[][]
        {
            new object[] {1, "em12", "去漆皮部套", 12, "DB_ControlNodesC5", 87, "NA1C101左缓存挡料气缸1", "左缓存挡料气缸1",
                "\"I132.0 左UPIN缓存挡料1气缸 伸出到位 NA1-C101a\"", 1056, "", "",
                "\"I132.1 左UPIN缓存挡料1气缸 缩回到位 NA1-C101b\"", 1057, "", "",
                "\"Q216.4 NA1C101挡料气缸1伸出\"", 1732, "\"Q216.5 NA1C101挡料气缸1缩回\"", 1733},

            new object[] {2, "", "", "", "", "", "NA1C201左缓存挡料气缸2", "左缓存挡料气缸2",
                "\"I132.2 左UPIN缓存挡料2气缸 伸出到位 NA1-C201a\"", 1058, "", "",
                "\"I132.3 左UPIN缓存挡料2气缸 缩回到位 NA1-C201b\"", 1059, "", "",
                "\"Q216.6 NA1C201挡料气缸2伸出\"", 1734, "\"Q216.7 NA1C201挡料气缸2缩回\"", 1735},

            new object[] {3, "", "", "", "", "", "NA1C301左缓存挡料气缸3", "左缓存挡料气缸3",
                "\"I132.4 左UPIN缓存挡料3气缸 伸出到位 NA1-C301a\"", 1060, "", "",
                "\"I132.5 左UPIN缓存挡料3气缸 缩回到位 NA1-C301b\"", 1061, "", "",
                "\"Q217.0 NA1C301挡料气缸3伸出\"", 1736, "\"Q217.1 NA1C301挡料气缸3缩回\"", 1737},

            new object[] {4, "", "", "", "", "", "NA1C401左缓存挡料气缸4", "左缓存挡料气缸4",
                "\"I15.2\"", 122, "", "", "\"I15.3\"", 123, "", "",
                "\"Q10.5\"", 85, "\"Q10.6\"", 86},

            new object[] {5, "", "", "", "", "", "NA1C401左缓存料盘抽出气缸", "左缓存料盘抽出气缸",
                "\"I132.6 左IPIN缓存料盘抽出气缸 伸出到位 NA1-C401a\"", 1062, "", "",
                "\"I132.7 左IPIN缓存料盘抽出气缸 缩回到位 NA1-C401b\"", 1063, "", "",
                "\"Q217.2 NA1C401料盘抽出气缸伸出\"", 1738, "\"Q217.3 NA1C401料盘抽出气缸缩回\"", 1739},

            new object[] {6, "", "", "", "", "", "NA1C501左缓存料盘定位气缸", "左缓存料盘定位气缸",
                "\"I133.0 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501a\"", 1064,
                "\"I133.2 左IPIN缓存料盘定位气缸 伸出到位 NA1-C501c\"", 1066,
                "\"I133.1 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501b\"", 1065,
                "\"I133.3 左IPIN缓存料盘定位气缸 缩回到位 NA1-C501d\"", 1067,
                "\"Q217.4 NA1C501料盘定位气缸伸出\"", 1740, "\"Q217.5 NA1C501料盘定位气缸缩回\"", 1741},

            new object[] {7, "", "", "", "", "", "NA1C601左缓存料盒搬运升降气缸", "左缓存料盒搬运升降气缸",
                "\"I133.4 左IPIN料盒搬运升降气缸 伸出到位 NA1-C601a\"", 1068, "", "",
                "\"I133.5 左IPIN料盒搬运升降气缸 缩回到位 NA1-C601b\"", 1069, "", "",
                "\"Q216.0 NA1C601料盒搬运升降气缸伸出\"", 1728, "\"Q216.1 NA1C601料盒搬运升降气缸缩回\"", 1729},

            new object[] {8, "", "", "", "", "", "NA1C701左缓存料盒搬运夹爪气缸", "左缓存料盒搬运夹爪气缸",
                "\"I133.6 左IPIN料盒夹爪气缸 伸出到位 NA1-C701a\"", 1070, "\"I15.0\"", 120,
                "\"I133.7 左IPIN料盒夹爪气缸 缩回到位 NA1-C701b\"", 1071, "\"I15.1\"", 121,
                "\"Q216.2 NA1C701料盒搬运夹爪气缸伸出\"", 1730, "\"Q216.3 NA1C701料盒搬运夹爪气缸缩回\"", 1731}
        };

        for (int row = 0; row < testData.Length; row++)
        {
            for (int col = 0; col < testData[row].Length; col++)
            {
                worksheet.Cells[row + 2, col + 1].Value = testData[row][col];
            }
        }

        // 保存文件
        var filePath = "manualRow_2_test.xlsx";
        package.SaveAs(new FileInfo(filePath));
        Console.WriteLine($"测试Excel文件已创建: {filePath}");
    }
}
